/**
 * Represents a missing skill that the candidate doesn't have
 */
export interface MissingSkill {
  name: string;
  level?: string | null;
  keywords?: string[] | null;
  category?: string; // Optional categorization for learning recommendations
}

/**
 * Main result interface for skill matching analysis
 */
export interface SkillMatchAnalysis {
  directMatches: DirectSkillMatch[];
  transferableSkills: TransferableSkillMatch[];
  missingSkills: MissingSkill[];
  summary: {
    totalJobSkills: number;
    directMatchCount: number;
    transferableMatchCount: number;
    missingSkillCount: number;
    coveragePercentage: number;
  };
}

/**
 * Represents a direct skill match between resume and job requirements
 */
export interface DirectSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  matchType: 'exact' | 'synonym' | 'keyword';
  sourceResume?: number; // Optional for introspection
}

/**
 * Represents a transferable skill match with AI-determined confidence rating
 */
export interface TransferableSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  confidenceRating: 1 | 2 | 3;
  reasoning: string;
  sourceResume?: number; // Optional for introspection
}

/**
 * Configuration options for skill matching analysis
 */
export interface SkillMatchOptions {
  includeSourceResume?: boolean; // For introspection
  maxTransferableSkills?: number; // Limit AI processing
  confidenceThreshold?: 1 | 2 | 3; // Minimum confidence to include
  timeoutMs?: number; // Timeout for AI operations in milliseconds
}

/**
 * Internal interface for consolidated skills from multiple resume variations
 */
export interface ConsolidatedSkill {
  name: string;
  level?: string;
  keywords: string[];
  sourceResumes: number[]; // Track which resumes contributed this skill
}
