export { analyzeSkillMatch, SkillMatchError } from './analyzer';
export { DirectMatchAnalyzer } from './direct-match-analyzer';
export { ResultAggregator } from './result-aggregator';
export { TransferableSkillExtractor } from './transferable-skills';

// Single-resume analysis exports
export {
  analyzeSingleResumeSkills,
  getSkillsBySource,
  getHighConfidenceTransferableSkills,
  getSkillsByMatchType,
  getMissingSkillsByCategory,
} from './single-resume-analyzer';

// Result reduction functions
export {
  reduceSkillAnalyses,
  getSkillsBySource as getReducedSkillsBySource,
  getHighConfidenceTransferableSkills as getReducedHighConfidenceTransferableSkills,
  getSkillsByMatchType as getReducedSkillsByMatchType,
  getMissingSkillsByCategory as getReducedMissingSkillsByCategory,
  getSkillCoverageByCategory,
} from './result-reducer';
export { SingleResumeSkillExtractor } from './single-resume-extractor';
export { SingleResumeDirectMatcher } from './single-resume-direct-matcher';
export { SingleResumeTransferableAnalyzer } from './single-resume-transferable-analyzer';

export type {
  ConsolidatedSkill,
  DirectSkillMatch,
  MissingSkill,
  SkillMatchAnalysis,
  SkillMatchOptions,
  TransferableSkillMatch,
} from './types';

// Single-resume types
export type {
  SingleResumeSkill,
  SingleResumeDirectSkillMatch,
  SingleResumeTransferableSkillMatch,
  SingleResumeMissingSkill,
  SingleResumeSkillAnalysis,
  SingleResumeSkillAnalysisOptions,
} from './single-resume-types';
export { SingleResumeSkillMatchError } from './single-resume-types';
