import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { SingleResumeDirectMatcher } from './single-resume-direct-matcher';
import { SingleResumeSkillExtractor } from './single-resume-extractor';
import { SingleResumeTransferableAnalyzer } from './single-resume-transferable-analyzer';
import type {
  SingleResumeSkillAnalysis,
  SingleResumeSkillAnalysisOptions,
} from './single-resume-types';
import { SingleResumeSkillMatchError } from './single-resume-types';

/**
 * Analyzes skill matches between a single resume and a job posting.
 *
 * This function processes exactly one resume against one job description,
 * extracting skills directly from the resume and performing direct/transferable
 * matching against job requirements without any consolidation logic.
 *
 * @param resume - Single resume to analyze
 * @param job - Job posting to match against
 * @param model - Lang<PERSON>hain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive single-resume skill analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeSingleResumeSkills(
 *   resume,
 *   jobPosting,
 *   mistralModel,
 *   { includeSourceDetails: true, confidenceThreshold: 2 }
 * );
 *
 * console.log(`Direct matches: ${analysis.directMatches.length}`);
 * console.log(`Coverage: ${analysis.summary.coveragePercentage}%`);
 * ```
 */
export async function analyzeSingleResumeSkills(
  resume: Resume,
  job: Job,
  model: BaseChatModel,
  options: SingleResumeSkillAnalysisOptions = {}
): Promise<SingleResumeSkillAnalysis> {
  try {
    validateInputs(resume, job, model);

    const finalOptions = {
      maxTransferableSkills: 20,
      confidenceThreshold: 1,
      timeoutMs: 30000, // 30 seconds default timeout
      includeSourceDetails: false,
      ...options,
    } satisfies SingleResumeSkillAnalysisOptions;

    // Step 1: Extract skills from the single resume
    const skillExtractor = new SingleResumeSkillExtractor(model);
    const resumeSkills = await skillExtractor.extractSkills(
      resume,
      finalOptions
    );

    // Step 2: Find direct matches (exact, synonym, keyword)
    const directMatcher = new SingleResumeDirectMatcher(model);
    const directMatches = await directMatcher.findDirectMatches(
      resumeSkills,
      job.skills,
      finalOptions
    );

    // Step 3: Identify unmatched skills for transferable analysis
    const { unmatchedResumeSkills, unmatchedJobSkills } =
      directMatcher.getUnmatchedSkills(resumeSkills, job.skills, directMatches);

    // Step 4: Analyze transferable skills using AI
    const transferableAnalyzer = new SingleResumeTransferableAnalyzer(model);
    const transferableSkills =
      await transferableAnalyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        finalOptions
      );

    // Step 5: Identify missing skills
    const missingSkills = transferableAnalyzer.identifyMissingSkills(
      job.skills,
      directMatches,
      transferableSkills
    );

    // Step 6: Calculate summary statistics
    const summary = calculateSummary(
      resumeSkills,
      job.skills,
      directMatches,
      transferableSkills,
      missingSkills
    );

    return {
      resumeSkills,
      directMatches,
      transferableSkills,
      missingSkills,
      summary,
    };
  } catch (error) {
    throw new SingleResumeSkillMatchError(
      `Failed to analyze single resume skills: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    );
  }
}

/**
 * Validates input parameters
 */
function validateInputs(
  resume: Resume,
  job: Job,
  model: BaseChatModel
): void {
  if (!resume) {
    throw new SingleResumeSkillMatchError('Resume is required');
  }

  if (!job) {
    throw new SingleResumeSkillMatchError('Job is required');
  }

  if (!model) {
    throw new SingleResumeSkillMatchError('Model is required');
  }

  if (!job.skills || job.skills.length === 0) {
    console.warn('Job has no skills defined, analysis will be limited');
  }
}

/**
 * Calculates summary statistics for the skill analysis
 */
function calculateSummary(
  resumeSkills: SingleResumeSkillAnalysis['resumeSkills'],
  jobSkills: Job['skills'],
  directMatches: SingleResumeSkillAnalysis['directMatches'],
  transferableSkills: SingleResumeSkillAnalysis['transferableSkills'],
  missingSkills: SingleResumeSkillAnalysis['missingSkills']
): SingleResumeSkillAnalysis['summary'] {
  const totalJobSkills = jobSkills?.length || 0;
  const totalResumeSkills = resumeSkills.length;
  const directMatchCount = directMatches.length;
  const transferableMatchCount = transferableSkills.length;
  const missingSkillCount = missingSkills.length;

  const totalMatches = directMatchCount + transferableMatchCount;
  const coveragePercentage =
    totalJobSkills > 0 ? Math.round((totalMatches / totalJobSkills) * 100) : 0;

  return {
    totalJobSkills,
    totalResumeSkills,
    directMatchCount,
    transferableMatchCount,
    missingSkillCount,
    coveragePercentage,
  };
}

/**
 * Helper function to get skills by source type
 */
export function getSkillsBySource(
  analysis: SingleResumeSkillAnalysis,
  source: 'explicit' | 'work_experience' | 'projects' | 'education'
) {
  return {
    skills: analysis.resumeSkills.filter((skill) => skill.source === source),
    directMatches: analysis.directMatches.filter(
      (match) => match.source === source
    ),
    transferableSkills: analysis.transferableSkills.filter(
      (match) => match.source === source
    ),
  };
}

/**
 * Helper function to get high-confidence transferable skills
 */
export function getHighConfidenceTransferableSkills(
  analysis: SingleResumeSkillAnalysis,
  minConfidence: 2 | 3 = 2
) {
  return analysis.transferableSkills.filter(
    (skill) => skill.confidenceRating >= minConfidence
  );
}

/**
 * Helper function to get skills by match type
 */
export function getSkillsByMatchType(
  analysis: SingleResumeSkillAnalysis,
  matchType: 'exact' | 'synonym' | 'keyword'
) {
  return analysis.directMatches.filter((match) => match.matchType === matchType);
}

/**
 * Helper function to get missing skills by category
 */
export function getMissingSkillsByCategory(
  analysis: SingleResumeSkillAnalysis,
  category?: string
) {
  if (!category) {
    return analysis.missingSkills;
  }
  return analysis.missingSkills.filter((skill) => skill.category === category);
}
