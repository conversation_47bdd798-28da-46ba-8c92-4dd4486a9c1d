import { z } from 'zod';

/**
 * Represents a skill extracted from a single resume
 */
export interface SingleResumeSkill {
  name: string;
  level?: string | null;
  keywords: string[];
  source: 'explicit' | 'work_experience' | 'projects' | 'education';
  sourceSection?: string; // e.g., "work_experience[0]", "projects[1]"
}

/**
 * Represents a direct skill match from a single resume
 */
export interface SingleResumeDirectSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  matchType: 'exact' | 'synonym' | 'keyword';
  source: 'explicit' | 'work_experience' | 'projects' | 'education';
  sourceSection?: string;
}

/**
 * Represents a transferable skill match from a single resume
 */
export interface SingleResumeTransferableSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  confidenceRating: 1 | 2 | 3;
  reasoning: string;
  source: 'explicit' | 'work_experience' | 'projects' | 'education';
  sourceSection?: string;
}

/**
 * Represents a missing skill that the single resume doesn't have
 */
export interface SingleResumeMissingSkill {
  name: string;
  level?: string | null;
  keywords?: string[] | null;
  category?: string;
}

/**
 * Main result interface for single-resume skill analysis
 */
export interface SingleResumeSkillAnalysis {
  resumeSkills: SingleResumeSkill[];
  directMatches: SingleResumeDirectSkillMatch[];
  transferableSkills: SingleResumeTransferableSkillMatch[];
  missingSkills: SingleResumeMissingSkill[];
  summary: {
    totalJobSkills: number;
    totalResumeSkills: number;
    directMatchCount: number;
    transferableMatchCount: number;
    missingSkillCount: number;
    coveragePercentage: number;
  };
}

/**
 * Configuration options for single-resume skill analysis
 */
export interface SingleResumeSkillAnalysisOptions {
  maxTransferableSkills?: number; // Limit AI processing
  confidenceThreshold?: 1 | 2 | 3; // Minimum confidence to include
  timeoutMs?: number; // Timeout for AI operations in milliseconds
  includeSourceDetails?: boolean; // Include source section information
}

/**
 * Error class for single-resume skill analysis failures
 */
export class SingleResumeSkillMatchError extends Error {
  constructor(
    message: string,
    public readonly cause?: unknown
  ) {
    super(message);
    this.name = 'SingleResumeSkillMatchError';
  }
}

// Zod schemas for AI-powered skill extraction and analysis

/**
 * Schema for skill extraction from text
 */
export const singleResumeSkillExtractionSchema = z.object({
  skills: z
    .array(z.string())
    .describe(
      'Array of technical skills, tools, frameworks, programming languages, and technologies found in the text'
    ),
});

/**
 * Schema for transferable skill analysis
 */
export const singleResumeTransferabilityAnalysisSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      confidenceRating: z.union([z.literal(1), z.literal(2), z.literal(3)]),
      reasoning: z.string().max(200),
    })
  ),
});

/**
 * Schema for synonym detection
 */
export const singleResumeSynonymDetectionSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      isSynonym: z.boolean(),
      reasoning: z.string().max(100),
    })
  ),
});
