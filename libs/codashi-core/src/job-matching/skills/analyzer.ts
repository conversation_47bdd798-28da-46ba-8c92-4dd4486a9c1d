import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { isAIUnavailableError, isTimeoutError } from '../../utils/common-utils';
import { DirectMatchAnalyzer } from './direct-match-analyzer';
import { ResultAggregator } from './result-aggregator';
import type { SkillMatchAnalysis, SkillMatchOptions } from './types';

// New single-resume architecture imports
import { reduceSkillAnalyses } from './result-reducer';
import { analyzeSingleResumeSkills } from './single-resume-analyzer';
import { SkillMatchError } from './types';

/**
 * Analyzes skill matches between multiple resume variations and a job posting.
 *
 * This function consolidates skills from all resume variations, performs direct
 * matching (exact, synonym, and keyword-based), and uses AI reasoning to identify
 * transferable skills with confidence ratings.
 *
 * @param resumeVariations - Array of resume variations for the same person
 * @param job - Job posting to match against
 * @param model - <PERSON><PERSON><PERSON><PERSON> BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive skill match analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeSkillMatch(
 *   [resume1, resume2],
 *   jobPosting,
 *   mistralModel,
 *   { includeSourceResume: true, confidenceThreshold: 2 }
 * );
 *
 * console.log(`Direct matches: ${analysis.directMatches.length}`);
 * console.log(`Coverage: ${analysis.summary.coveragePercentage}%`);
 * ```
 */
export async function analyzeSkillMatch(
  resumeVariations: Resume[],
  job: Job,
  model: BaseChatModel,
  options: SkillMatchOptions = {}
): Promise<SkillMatchAnalysis> {
  validateInputs(resumeVariations, job, model);

  const finalOptions = {
    includeSourceResume: false,
    maxTransferableSkills: 20,
    confidenceThreshold: 1,
    timeoutMs: 30000, // 30 seconds default timeout
    ...options,
  } satisfies SkillMatchOptions;

  try {
    // NEW ARCHITECTURE: Use single-resume analysis for each resume
    // then reduce the results into the consolidated format

    // Convert options to single-resume format
    const singleResumeOptions = {
      maxTransferableSkills: finalOptions.maxTransferableSkills,
      confidenceThreshold: finalOptions.confidenceThreshold,
      timeoutMs: finalOptions.timeoutMs,
      includeSourceDetails: finalOptions.includeSourceResume,
    };

    // Analyze each resume individually
    const singleResumeAnalyses = await Promise.all(
      resumeVariations.map((resume) =>
        analyzeSingleResumeSkills(resume, job, model, singleResumeOptions)
      )
    );

    // Reduce the individual analyses into the consolidated format
    const consolidatedResult = reduceSkillAnalyses(singleResumeAnalyses);

    return consolidatedResult;
  } catch (error) {
    // Handle timeout errors
    if (isTimeoutError(error)) {
      throw new SkillMatchError(
        'Skill matching analysis timed out. Try reducing the number of resume variations or increasing the timeout.',
        'TIMEOUT'
      );
    }

    // Handle AI unavailability
    if (isAIUnavailableError(error)) {
      throw new SkillMatchError(
        'AI model is unavailable. Skill matching could not be performed.',
        'AI_UNAVAILABLE'
      );
    }

    // Handle unexpected errors
    throw new SkillMatchError(
      `Unexpected error during skill matching: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      'INVALID_INPUT'
    );
  }
}

/**
 * Validates input parameters for the analyzeSkillMatch function
 */
function validateInputs(
  resumeVariations: Resume[],
  job: Job,
  model: BaseChatModel
): void {
  // Validate resume variations
  if (!Array.isArray(resumeVariations)) {
    throw new SkillMatchError(
      'resumeVariations must be an array',
      'INVALID_INPUT'
    );
  }

  if (resumeVariations.length === 0) {
    throw new SkillMatchError(
      'At least one resume variation is required',
      'INVALID_INPUT'
    );
  }

  // Validate each resume has the required structure
  for (let i = 0; i < resumeVariations.length; i++) {
    const resume = resumeVariations[i];
    if (!resume || typeof resume !== 'object') {
      throw new SkillMatchError(
        `Resume variation at index ${i} is invalid`,
        'INVALID_INPUT'
      );
    }

    if (!Array.isArray(resume.sections)) {
      throw new SkillMatchError(
        `Resume variation at index ${i} must have a sections array`,
        'INVALID_INPUT'
      );
    }
  }

  // Validate job object
  if (!job || typeof job !== 'object') {
    throw new SkillMatchError(
      'job must be a valid Job object',
      'INVALID_INPUT'
    );
  }

  // Validate job has skills array (can be empty or null but if present must be an array)
  if (
    job.skills !== null &&
    job.skills !== undefined &&
    !Array.isArray(job.skills)
  ) {
    throw new SkillMatchError(
      'job.skills must be an array or null',
      'INVALID_INPUT'
    );
  }

  // Validate job skills structure if skills array exists
  if (job.skills && Array.isArray(job.skills)) {
    for (let i = 0; i < job.skills.length; i++) {
      const skill = job.skills[i];
      if (!skill || typeof skill !== 'object' || !skill.name) {
        throw new SkillMatchError(
          `Job skill at index ${i} must have a name property`,
          'INVALID_INPUT'
        );
      }
    }
  }

  // Validate model
  if (!model) {
    throw new SkillMatchError(
      'model is required for AI-powered analysis',
      'INVALID_INPUT'
    );
  }

  // Validate that model has required methods
  if (typeof model.invoke !== 'function') {
    throw new SkillMatchError(
      'model must be a valid LangChain BaseChatModel',
      'INVALID_INPUT'
    );
  }
}

/**
 * Identifies skills that didn't have direct matches for transferable analysis
 */
function identifyUnmatchedSkills(
  consolidatedSkills: import('./types').ConsolidatedSkill[],
  jobSkills: Job['skills'],
  directMatches: Awaited<ReturnType<DirectMatchAnalyzer['findDirectMatches']>>
) {
  // Create sets of matched skills for efficient lookup
  const matchedResumeSkills = new Set(
    directMatches.map((match) => match.resumeSkill.toLowerCase().trim())
  );

  const matchedJobSkills = new Set(
    directMatches.map((match) => match.jobSkill.toLowerCase().trim())
  );

  // Find unmatched resume skills
  const unmatchedResumeSkills = consolidatedSkills.filter(
    (skill) => !matchedResumeSkills.has(skill.name.toLowerCase().trim())
  );

  // Find unmatched job skills
  const unmatchedJobSkills =
    jobSkills?.filter(
      (skill) => !matchedJobSkills.has(skill.name.toLowerCase().trim())
    ) || [];

  return {
    unmatchedResumeSkills,
    unmatchedJobSkills,
  };
}

/**
 * Creates partial results when AI analysis fails
 */
function createPartialResults(
  directMatches: Awaited<ReturnType<DirectMatchAnalyzer['findDirectMatches']>>,
  jobSkills: Job['skills'],
  options: SkillMatchOptions
): Partial<SkillMatchAnalysis> {
  const resultAggregator = new ResultAggregator();
  return resultAggregator.aggregate(
    directMatches,
    [], // No transferable skills due to AI failure
    jobSkills,
    options
  );
}
