import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';

import type { Job } from '../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import type { ConsolidatedSkill, DirectSkillMatch } from './types';

/**
 * Schema for AI-powered synonym detection results
 */
const synonymDetectionSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      isSynonym: z.boolean(),
      reasoning: z.string().optional(),
    })
  ),
});

/**
 * Analyzes direct skill matches between consolidated resume skills and job requirements.
 *
 * This class performs exact matching, keyword matching, and AI-powered synonym detection
 * to identify direct skill matches between resume and job requirements.
 */
export class DirectMatchAnalyzer {
  constructor(private model: BaseChatModel) {}

  /**
   * Finds all direct matches between consolidated resume skills and job skills.
   *
   * @param consolidatedSkills - Skills consolidated from multiple resume variations
   * @param jobSkills - Skills required by the job posting
   * @param options - Configuration options
   * @returns Array of direct skill matches
   */
  async findDirectMatches(
    consolidatedSkills: ConsolidatedSkill[],
    jobSkills: Job['skills'],
    options: { includeSourceResume?: boolean; timeoutMs?: number } = {}
  ): Promise<DirectSkillMatch[]> {
    if (!jobSkills || jobSkills.length === 0) {
      return [];
    }

    const matches: DirectSkillMatch[] = [];
    const unmatchedResumeSkills: ConsolidatedSkill[] = [];
    const unmatchedJobSkills: Job['skills'] = [];

    // First pass: exact matches
    for (const resumeSkill of consolidatedSkills) {
      let matched = false;

      for (const jobSkill of jobSkills) {
        if (this.isExactMatch(resumeSkill.name, jobSkill.name)) {
          matches.push({
            jobSkill: jobSkill.name,
            resumeSkill: resumeSkill.name,
            matchType: 'exact',
            ...(options.includeSourceResume && {
              sourceResume: resumeSkill.sourceResumes[0],
            }),
          });
          matched = true;
          break;
        }
      }

      if (!matched) {
        unmatchedResumeSkills.push(resumeSkill);
      }
    }

    // Collect unmatched job skills for further analysis
    for (const jobSkill of jobSkills) {
      const isMatched = matches.some(
        (match) => match.jobSkill === jobSkill.name
      );
      if (!isMatched) {
        unmatchedJobSkills.push(jobSkill);
      }
    }

    // Second pass: keyword matches
    const keywordMatches = this.findKeywordMatches(
      unmatchedResumeSkills,
      unmatchedJobSkills,
      options
    );
    matches.push(...keywordMatches);

    // Update unmatched lists after keyword matching
    const keywordMatchedResumeSkills = new Set(
      keywordMatches.map((m) => m.resumeSkill)
    );
    const keywordMatchedJobSkills = new Set(
      keywordMatches.map((m) => m.jobSkill)
    );

    const remainingResumeSkills = unmatchedResumeSkills.filter(
      (skill) => !keywordMatchedResumeSkills.has(skill.name)
    );
    const remainingJobSkills = unmatchedJobSkills.filter(
      (skill) => !keywordMatchedJobSkills.has(skill.name)
    );

    // Third pass: AI-powered synonym detection
    if (remainingResumeSkills.length > 0 && remainingJobSkills.length > 0) {
      try {
        const synonymMatches = await this.findSynonymMatches(
          remainingResumeSkills,
          remainingJobSkills,
          options
        );
        matches.push(...synonymMatches);
      } catch (error) {
        // Gracefully handle AI failures - log but don't throw
        if (isAIUnavailableError(error)) {
          console.warn(
            'AI model unavailable for synonym detection, using exact and keyword matches only'
          );
        } else if (isTimeoutError(error)) {
          console.warn(
            'AI synonym detection timed out, using exact and keyword matches only'
          );
        } else {
          console.warn(
            'AI synonym detection failed, falling back to basic matching:',
            error
          );
        }
      }
    }

    return matches;
  }

  /**
   * Checks if two skill names are exact matches (case-insensitive).
   *
   * @param resumeSkill - Skill name from resume
   * @param jobSkill - Skill name from job posting
   * @returns True if skills match exactly
   */
  private isExactMatch(resumeSkill: string, jobSkill: string): boolean {
    return resumeSkill.toLowerCase().trim() === jobSkill.toLowerCase().trim();
  }

  /**
   * Finds keyword matches between resume skills and job requirements.
   *
   * @param resumeSkills - Unmatched resume skills
   * @param jobSkills - Unmatched job skills
   * @param options - Configuration options
   * @returns Array of keyword matches
   */
  private findKeywordMatches(
    resumeSkills: ConsolidatedSkill[],
    jobSkills: Job['skills'],
    options: { includeSourceResume?: boolean } = {}
  ): DirectSkillMatch[] {
    const matches: DirectSkillMatch[] = [];

    if (!jobSkills || jobSkills.length === 0) {
      return matches;
    }

    for (const resumeSkill of resumeSkills) {
      for (const jobSkill of jobSkills) {
        if (this.isKeywordMatch(resumeSkill, jobSkill)) {
          matches.push({
            jobSkill: jobSkill.name,
            resumeSkill: resumeSkill.name,
            matchType: 'keyword',
            ...(options.includeSourceResume && {
              sourceResume: resumeSkill.sourceResumes[0],
            }),
          });
          break; // Only match each resume skill once
        }
      }
    }

    return matches;
  }

  /**
   * Checks if a resume skill matches job skill keywords or vice versa.
   *
   * @param resumeSkill - Consolidated skill from resume
   * @param jobSkill - Skill requirement from job posting
   * @returns True if there's a keyword match
   */
  private isKeywordMatch(
    resumeSkill: ConsolidatedSkill,
    jobSkill: NonNullable<Job['skills']>[number]
  ): boolean {
    const resumeSkillLower = resumeSkill.name.toLowerCase();
    const jobSkillLower = jobSkill.name.toLowerCase();

    // Check if resume skill name matches any job skill keywords
    if (jobSkill.keywords) {
      for (const keyword of jobSkill.keywords) {
        if (resumeSkillLower === keyword.toLowerCase().trim()) {
          return true;
        }
      }
    }

    // Check if any resume skill keywords match the job skill name
    if (resumeSkill.keywords) {
      for (const keyword of resumeSkill.keywords) {
        if (keyword.toLowerCase().trim() === jobSkillLower) {
          return true;
        }
      }
    }

    // Check if any resume skill keywords match any job skill keywords
    if (resumeSkill.keywords && jobSkill.keywords) {
      for (const resumeKeyword of resumeSkill.keywords) {
        for (const jobKeyword of jobSkill.keywords) {
          if (
            resumeKeyword.toLowerCase().trim() ===
            jobKeyword.toLowerCase().trim()
          ) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * Uses AI to detect synonym matches between resume and job skills.
   *
   * @param resumeSkills - Unmatched resume skills
   * @param jobSkills - Unmatched job skills
   * @param options - Configuration options
   * @returns Array of synonym matches
   */
  private async findSynonymMatches(
    resumeSkills: ConsolidatedSkill[],
    jobSkills: Job['skills'],
    options: { includeSourceResume?: boolean; timeoutMs?: number } = {}
  ): Promise<DirectSkillMatch[]> {
    if (resumeSkills.length === 0 || !jobSkills || jobSkills.length === 0) {
      return [];
    }

    // Create skill comparison pairs for batch processing
    const skillPairs: Array<{
      resumeSkill: ConsolidatedSkill;
      jobSkill: NonNullable<Job['skills']>[number];
    }> = [];

    for (const resumeSkill of resumeSkills) {
      for (const jobSkill of jobSkills) {
        skillPairs.push({ resumeSkill, jobSkill });
      }
    }

    // Process in batches to avoid overwhelming the AI model
    const batchSize = 10;
    const matches: DirectSkillMatch[] = [];

    for (let i = 0; i < skillPairs.length; i += batchSize) {
      const batch = skillPairs.slice(i, i + batchSize);
      const batchOperation = this.processSynonymBatch(batch, options);

      const batchMatches = options.timeoutMs
        ? await withTimeout(
            batchOperation,
            options.timeoutMs,
            `Synonym detection batch ${i / batchSize + 1}`
          )
        : await batchOperation;

      matches.push(...batchMatches);
    }

    return matches;
  }

  /**
   * Processes a batch of skill pairs for synonym detection.
   *
   * @param batch - Batch of skill pairs to analyze
   * @param options - Configuration options
   * @returns Array of synonym matches from this batch
   */
  private async processSynonymBatch(
    batch: Array<{
      resumeSkill: ConsolidatedSkill;
      jobSkill: NonNullable<Job['skills']>[number];
    }>,
    options: { includeSourceResume?: boolean; timeoutMs?: number } = {}
  ): Promise<DirectSkillMatch[]> {
    const prompt = this.createSynonymDetectionPrompt();
    const parser = StructuredOutputParser.fromZodSchema(synonymDetectionSchema);

    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const skillComparisons = batch.map(({ resumeSkill, jobSkill }) => ({
      resumeSkill: resumeSkill.name,
      jobSkill: jobSkill.name,
    }));

    const result = await chain.invoke({
      skill_comparisons: JSON.stringify(skillComparisons, null, 2),
      format_instructions: parser.getFormatInstructions(),
    });

    // Convert AI results to DirectSkillMatch format
    const matches: DirectSkillMatch[] = [];

    for (const match of result.matches) {
      if (match.isSynonym) {
        // Find the original resume skill to get source information
        const originalResumeSkill = batch.find(
          ({ resumeSkill }) => resumeSkill.name === match.resumeSkill
        )?.resumeSkill;

        matches.push({
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          matchType: 'synonym',
          ...(options.includeSourceResume &&
            originalResumeSkill && {
              sourceResume: originalResumeSkill.sourceResumes[0],
            }),
        });
      }
    }

    return matches;
  }

  /**
   * Creates the prompt template for AI-powered synonym detection.
   *
   * @returns ChatPromptTemplate for synonym detection
   */
  private createSynonymDetectionPrompt(): ChatPromptTemplate {
    return ChatPromptTemplate.fromTemplate(`
You are an expert in technology skills and job requirements. Your task is to identify if resume skills are synonymous with job requirements.

Two skills are considered synonymous if they refer to the same technology, tool, or concept, even if they use different names or abbreviations.

Examples of synonymous skills:
- "JavaScript" and "JS"
- "Vue" and "Vue.js"
- "Node" and "Node.js"
- "React" and "ReactJS"
- "PostgreSQL" and "Postgres"
- "Docker" and "Docker containers"

Examples of NON-synonymous skills:
- "React" and "Angular" (different frameworks)
- "Python" and "Java" (different languages)
- "MySQL" and "PostgreSQL" (different databases, though similar)

Analyze the following skill comparisons and determine if each pair represents synonymous skills:

{skill_comparisons}

For each comparison, provide:
1. Whether the skills are synonymous (true/false)
2. Brief reasoning if they are synonymous

{format_instructions}
`);
  }
}
