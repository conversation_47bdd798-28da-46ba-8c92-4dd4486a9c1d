import { describe, it, expect } from 'vitest';

import { reduceSkillAnalyses } from './result-reducer';
import type {
  SingleResumeSkillAnalysis,
  SingleResumeSkill,
  SingleResumeDirectSkillMatch,
  SingleResumeTransferableSkillMatch,
  SingleResumeMissingSkill,
} from './single-resume-types';

describe('Skills Result Reducer', () => {
  const createMockSkill = (
    name: string,
    source: 'explicit' | 'work_experience' | 'projects' | 'education' = 'explicit',
    sourceSection?: string
  ): SingleResumeSkill => ({
    name,
    level: null,
    keywords: null,
    source,
    sourceSection,
  });

  const createMockDirectMatch = (
    resumeSkill: string,
    jobSkill: string,
    matchType: 'exact' | 'synonym' | 'keyword' = 'exact',
    confidence: number = 1.0
  ): SingleResumeDirectSkillMatch => ({
    resumeSkill,
    jobSkill,
    matchType,
    confidence,
    source: 'explicit',
    sourceSection: undefined,
  });

  const createMockTransferableMatch = (
    resumeSkill: string,
    jobSkill: string,
    confidenceRating: 1 | 2 | 3 = 2
  ): SingleResumeTransferableSkillMatch => ({
    resumeSkill,
    jobSkill,
    confidenceRating,
    reasoning: `${resumeSkill} transfers to ${jobSkill}`,
    source: 'work_experience',
    sourceSection: undefined,
  });

  const createMockMissingSkill = (name: string): SingleResumeMissingSkill => ({
    name,
    level: null,
    keywords: null,
    category: 'Technical',
  });

  const createMockAnalysis = (
    resumeIndex: number,
    skills: SingleResumeSkill[],
    directMatches: SingleResumeDirectSkillMatch[],
    transferableSkills: SingleResumeTransferableSkillMatch[],
    missingSkills: SingleResumeMissingSkill[]
  ): SingleResumeSkillAnalysis => ({
    resumeSkills: skills,
    directMatches,
    transferableSkills,
    missingSkills,
    summary: {
      totalResumeSkills: skills.length,
      totalJobSkills: directMatches.length + transferableSkills.length + missingSkills.length,
      directMatchCount: directMatches.length,
      transferableMatchCount: transferableSkills.length,
      missingSkillCount: missingSkills.length,
      overallCoverage: directMatches.length > 0 ? 0.8 : 0.5,
      directMatchPercentage: directMatches.length > 0 ? 0.6 : 0.0,
      transferableMatchPercentage: transferableSkills.length > 0 ? 0.3 : 0.0,
    },
  });

  describe('reduceSkillAnalyses', () => {
    it('should combine skills from multiple single-resume analyses', () => {
      const skills1 = [createMockSkill('JavaScript'), createMockSkill('React')];
      const skills2 = [createMockSkill('Python'), createMockSkill('Django')];

      const directMatches1 = [createMockDirectMatch('JavaScript', 'JavaScript')];
      const directMatches2 = [createMockDirectMatch('Python', 'Python')];

      const analysis1 = createMockAnalysis(0, skills1, directMatches1, [], []);
      const analysis2 = createMockAnalysis(1, skills2, directMatches2, [], []);

      const result = reduceSkillAnalyses([analysis1, analysis2]);

      expect(result.resumeSkills).toHaveLength(4);
      expect(result.resumeSkills.map((s) => s.name)).toEqual([
        'JavaScript',
        'React',
        'Python',
        'Django',
      ]);
      expect(result.directMatches).toHaveLength(2);
    });

    it('should deduplicate identical direct matches', () => {
      const skills1 = [createMockSkill('JavaScript')];
      const skills2 = [createMockSkill('JavaScript')];

      const directMatches1 = [createMockDirectMatch('JavaScript', 'JavaScript')];
      const directMatches2 = [createMockDirectMatch('JavaScript', 'JavaScript')];

      const analysis1 = createMockAnalysis(0, skills1, directMatches1, [], []);
      const analysis2 = createMockAnalysis(1, skills2, directMatches2, [], []);

      const result = reduceSkillAnalyses([analysis1, analysis2]);

      // Should deduplicate identical matches
      expect(result.directMatches).toHaveLength(1);
      expect(result.directMatches[0].resumeSkill).toBe('JavaScript');
      expect(result.directMatches[0].jobSkill).toBe('JavaScript');
    });

    it('should keep highest confidence transferable skills', () => {
      const transferable1 = [
        createMockTransferableMatch('Java', 'JavaScript', 2),
        createMockTransferableMatch('C++', 'Python', 1),
      ];
      const transferable2 = [
        createMockTransferableMatch('Java', 'JavaScript', 3), // Higher confidence
        createMockTransferableMatch('Ruby', 'Python', 2),
      ];

      const analysis1 = createMockAnalysis(0, [], [], transferable1, []);
      const analysis2 = createMockAnalysis(1, [], [], transferable2, []);

      const result = reduceSkillAnalyses([analysis1, analysis2]);

      // Should keep the higher confidence match for Java -> JavaScript
      const javaMatch = result.transferableSkills.find(
        (t) => t.resumeSkill === 'Java' && t.jobSkill === 'JavaScript'
      );
      expect(javaMatch?.confidenceRating).toBe(3);

      // Should include all unique transferable matches
      expect(result.transferableSkills).toHaveLength(3);
    });

    it('should consolidate missing skills across resumes', () => {
      const missing1 = [createMockMissingSkill('Docker'), createMockMissingSkill('AWS')];
      const missing2 = [createMockMissingSkill('Docker'), createMockMissingSkill('Kubernetes')];

      const analysis1 = createMockAnalysis(0, [], [], [], missing1);
      const analysis2 = createMockAnalysis(1, [], [], [], missing2);

      const result = reduceSkillAnalyses([analysis1, analysis2]);

      // Should deduplicate missing skills
      expect(result.missingSkills).toHaveLength(3);
      expect(result.missingSkills.map((s) => s.name)).toEqual([
        'Docker',
        'AWS',
        'Kubernetes',
      ]);
    });

    it('should calculate correct summary statistics', () => {
      const skills1 = [createMockSkill('JavaScript'), createMockSkill('React')];
      const skills2 = [createMockSkill('Python')];

      const directMatches1 = [createMockDirectMatch('JavaScript', 'JavaScript')];
      const directMatches2 = [createMockDirectMatch('Python', 'Python')];

      const transferable1 = [createMockTransferableMatch('React', 'Vue.js', 2)];
      const missing1 = [createMockMissingSkill('Docker')];

      const analysis1 = createMockAnalysis(0, skills1, directMatches1, transferable1, missing1);
      const analysis2 = createMockAnalysis(1, skills2, directMatches2, [], []);

      const result = reduceSkillAnalyses([analysis1, analysis2]);

      expect(result.summary.totalResumeSkills).toBe(3);
      expect(result.summary.directMatchCount).toBe(2);
      expect(result.summary.transferableMatchCount).toBe(1);
      expect(result.summary.missingSkillCount).toBe(1);

      // Total job skills = direct + transferable + missing
      expect(result.summary.totalJobSkills).toBe(4);

      // Coverage = (direct + transferable) / total job skills
      expect(result.summary.overallCoverage).toBeCloseTo(0.75, 2);

      // Direct match percentage = direct / total job skills
      expect(result.summary.directMatchPercentage).toBeCloseTo(0.5, 2);

      // Transferable match percentage = transferable / total job skills
      expect(result.summary.transferableMatchPercentage).toBeCloseTo(0.25, 2);
    });

    it('should handle empty analyses array', () => {
      const result = reduceSkillAnalyses([]);

      expect(result.resumeSkills).toHaveLength(0);
      expect(result.directMatches).toHaveLength(0);
      expect(result.transferableSkills).toHaveLength(0);
      expect(result.missingSkills).toHaveLength(0);
      expect(result.summary.totalResumeSkills).toBe(0);
      expect(result.summary.totalJobSkills).toBe(0);
      expect(result.summary.overallCoverage).toBe(0);
    });

    it('should handle analyses with no skills', () => {
      const emptyAnalysis: SingleResumeSkillAnalysis = {
        resumeSkills: [],
        directMatches: [],
        transferableSkills: [],
        missingSkills: [],
        summary: {
          totalResumeSkills: 0,
          totalJobSkills: 0,
          directMatchCount: 0,
          transferableMatchCount: 0,
          missingSkillCount: 0,
          overallCoverage: 0,
          directMatchPercentage: 0,
          transferableMatchPercentage: 0,
        },
      };

      const result = reduceSkillAnalyses([emptyAnalysis]);

      expect(result.resumeSkills).toHaveLength(0);
      expect(result.directMatches).toHaveLength(0);
      expect(result.transferableSkills).toHaveLength(0);
      expect(result.missingSkills).toHaveLength(0);
    });

    it('should preserve skill source information when available', () => {
      const skills1 = [
        createMockSkill('JavaScript', 'explicit', 'skills'),
        createMockSkill('React', 'work_experience', 'work-1'),
      ];

      const directMatches1 = [
        { ...createMockDirectMatch('JavaScript', 'JavaScript'), sourceSection: 'skills' },
        { ...createMockDirectMatch('React', 'React'), sourceSection: 'work-1' },
      ];

      const analysis1 = createMockAnalysis(0, skills1, directMatches1, [], []);

      const result = reduceSkillAnalyses([analysis1]);

      expect(result.resumeSkills[0].sourceSection).toBe('skills');
      expect(result.resumeSkills[1].sourceSection).toBe('work-1');
      expect(result.directMatches[0].sourceSection).toBe('skills');
      expect(result.directMatches[1].sourceSection).toBe('work-1');
    });
  });
});
