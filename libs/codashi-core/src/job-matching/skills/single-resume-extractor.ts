import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Resume } from '../../entities/resume';
import { withTimeout } from '../../utils/common-utils';
import type {
  SingleResumeSkill,
  SingleResumeSkillAnalysisOptions,
} from './single-resume-types';
import { singleResumeSkillExtractionSchema } from './single-resume-types';

/**
 * Extracts skills from a single resume without consolidation logic.
 * Processes exactly one resume and extracts skills from all sections.
 */
export class SingleResumeSkillExtractor {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Extracts all skills from a single resume
   *
   * @param resume - Single resume to extract skills from
   * @param options - Configuration options
   * @returns Promise resolving to array of skills with source information
   */
  async extractSkills(
    resume: Resume,
    options: SingleResumeSkillAnalysisOptions = {}
  ): Promise<SingleResumeSkill[]> {
    const allSkills: SingleResumeSkill[] = [];

    // Extract explicit skills from skills section
    if (resume.sections) {
      const skillsSection = resume.sections.find(
        (section) => section.name.toLowerCase() === 'skills'
      );
      if (skillsSection?.items) {
        for (const [itemIndex, item] of skillsSection.items.entries()) {
          if (item.name) {
            allSkills.push({
              name: item.name,
              level: item.level || null,
              keywords: item.keywords || [],
              source: 'explicit',
              sourceSection: options.includeSourceDetails
                ? `skills[${itemIndex}]`
                : undefined,
            });
          }
        }
      }
    }

    // Extract skills from work experience
    if (resume.work_experience) {
      for (const [expIndex, experience] of resume.work_experience.entries()) {
        const workSkills = await this.extractSkillsFromWorkExperience(
          experience,
          expIndex,
          options
        );
        allSkills.push(...workSkills);
      }
    }

    // Extract skills from projects
    if (resume.projects) {
      for (const [projIndex, project] of resume.projects.entries()) {
        const projectSkills = await this.extractSkillsFromProject(
          project,
          projIndex,
          options
        );
        allSkills.push(...projectSkills);
      }
    }

    // Extract skills from education
    if (resume.education) {
      for (const [eduIndex, education] of resume.education.entries()) {
        const educationSkills = await this.extractSkillsFromEducation(
          education,
          eduIndex,
          options
        );
        allSkills.push(...educationSkills);
      }
    }

    // Deduplicate skills while preserving source information
    return this.deduplicateSkills(allSkills);
  }

  /**
   * Extracts skills from a work experience entry
   */
  private async extractSkillsFromWorkExperience(
    experience: Resume['work_experience'][0],
    index: number,
    options: SingleResumeSkillAnalysisOptions
  ): Promise<SingleResumeSkill[]> {
    const skills: SingleResumeSkill[] = [];

    // Extract from description
    if (experience.description) {
      const extractedSkills = await this.extractSkillsFromText(
        experience.description,
        options
      );
      skills.push(
        ...extractedSkills.map((skill) => ({
          name: skill,
          level: null,
          keywords: [],
          source: 'work_experience' as const,
          sourceSection: options.includeSourceDetails
            ? `work_experience[${index}].description`
            : undefined,
        }))
      );
    }

    // Extract from highlights
    if (experience.highlights) {
      for (const [highlightIndex, highlight] of experience.highlights.entries()) {
        const extractedSkills = await this.extractSkillsFromText(
          highlight,
          options
        );
        skills.push(
          ...extractedSkills.map((skill) => ({
            name: skill,
            level: null,
            keywords: [],
            source: 'work_experience' as const,
            sourceSection: options.includeSourceDetails
              ? `work_experience[${index}].highlights[${highlightIndex}]`
              : undefined,
          }))
        );
      }
    }

    return skills;
  }

  /**
   * Extracts skills from a project entry
   */
  private async extractSkillsFromProject(
    project: Resume['projects'][0],
    index: number,
    options: SingleResumeSkillAnalysisOptions
  ): Promise<SingleResumeSkill[]> {
    const skills: SingleResumeSkill[] = [];

    // Extract from description
    if (project.description) {
      const extractedSkills = await this.extractSkillsFromText(
        project.description,
        options
      );
      skills.push(
        ...extractedSkills.map((skill) => ({
          name: skill,
          level: null,
          keywords: [],
          source: 'projects' as const,
          sourceSection: options.includeSourceDetails
            ? `projects[${index}].description`
            : undefined,
        }))
      );
    }

    // Extract from keywords
    if (project.keywords) {
      skills.push(
        ...project.keywords.map((keyword) => ({
          name: keyword,
          level: null,
          keywords: [],
          source: 'projects' as const,
          sourceSection: options.includeSourceDetails
            ? `projects[${index}].keywords`
            : undefined,
        }))
      );
    }

    return skills;
  }

  /**
   * Extracts skills from an education entry
   */
  private async extractSkillsFromEducation(
    education: Resume['education'][0],
    index: number,
    options: SingleResumeSkillAnalysisOptions
  ): Promise<SingleResumeSkill[]> {
    const skills: SingleResumeSkill[] = [];

    // Extract from courses
    if (education.courses) {
      for (const course of education.courses) {
        const extractedSkills = await this.extractSkillsFromText(
          course,
          options
        );
        skills.push(
          ...extractedSkills.map((skill) => ({
            name: skill,
            level: null,
            keywords: [],
            source: 'education' as const,
            sourceSection: options.includeSourceDetails
              ? `education[${index}].courses`
              : undefined,
          }))
        );
      }
    }

    return skills;
  }

  /**
   * Extracts skills from free text using AI
   */
  private async extractSkillsFromText(
    text: string,
    options: SingleResumeSkillAnalysisOptions
  ): Promise<string[]> {
    if (!text.trim()) {
      return [];
    }

    try {
      const prompt = ChatPromptTemplate.fromTemplate(`
Extract technical skills, tools, frameworks, programming languages, and technologies from the following text.
Focus on concrete, specific technologies rather than soft skills or general concepts.

Text: {text}

{format_instructions}
      `);

      const parser = StructuredOutputParser.fromZodSchema(
        singleResumeSkillExtractionSchema
      );
      const chain = RunnableSequence.from([prompt, this.model, parser]);

      const operation = chain.invoke({
        text,
        format_instructions: parser.getFormatInstructions(),
      });

      const result = options.timeoutMs
        ? await withTimeout(operation, options.timeoutMs, 'Skill extraction')
        : await operation;

      return result.skills || [];
    } catch (error) {
      console.warn(
        'Failed to extract skills using AI, falling back to pattern matching:',
        error
      );
      return this.extractSkillsFromTextFallback(text);
    }
  }

  /**
   * Fallback skill extraction using pattern matching
   */
  private extractSkillsFromTextFallback(text: string): string[] {
    const skillPatterns = [
      // Programming languages
      /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|Scala)\b/gi,
      // Frameworks and libraries
      /\b(React|Angular|Vue|Node\.js|Express|Django|Flask|Spring|Laravel|Rails)\b/gi,
      // Databases
      /\b(MySQL|PostgreSQL|MongoDB|Redis|SQLite|Oracle|SQL Server)\b/gi,
      // Cloud platforms
      /\b(AWS|Azure|GCP|Google Cloud|Heroku|Vercel|Netlify)\b/gi,
      // Tools
      /\b(Git|Docker|Kubernetes|Jenkins|Terraform|Ansible)\b/gi,
    ];

    const skills = new Set<string>();
    for (const pattern of skillPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach((match) => skills.add(match));
      }
    }

    return Array.from(skills);
  }

  /**
   * Deduplicates skills while preserving the most informative source
   */
  private deduplicateSkills(skills: SingleResumeSkill[]): SingleResumeSkill[] {
    const skillMap = new Map<string, SingleResumeSkill>();

    for (const skill of skills) {
      const normalizedName = skill.name.toLowerCase().trim();
      const existing = skillMap.get(normalizedName);

      if (!existing) {
        skillMap.set(normalizedName, skill);
      } else {
        // Prefer explicit skills over extracted ones
        if (skill.source === 'explicit' && existing.source !== 'explicit') {
          skillMap.set(normalizedName, skill);
        }
        // Merge keywords if both have them
        else if (skill.keywords.length > 0 || existing.keywords.length > 0) {
          const mergedKeywords = [
            ...new Set([...existing.keywords, ...skill.keywords]),
          ];
          skillMap.set(normalizedName, {
            ...existing,
            keywords: mergedKeywords,
          });
        }
      }
    }

    return Array.from(skillMap.values());
  }
}
