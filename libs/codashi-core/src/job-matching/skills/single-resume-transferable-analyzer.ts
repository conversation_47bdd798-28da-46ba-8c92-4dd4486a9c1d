import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import type {
  SingleResumeSkill,
  SingleResumeSkillAnalysisOptions,
  SingleResumeTransferableSkillMatch,
} from './single-resume-types';
import { singleResumeTransferabilityAnalysisSchema } from './single-resume-types';

/**
 * Analyzes transferable skills from a single resume using AI reasoning.
 * Identifies skills that could transfer to job requirements with confidence ratings.
 */
export class SingleResumeTransferableAnalyzer {
  private model: BaseChatModel;
  private readonly MAX_SAFE_TOKENS = 3000; // Conservative token limit

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Analyzes transferable skills between unmatched resume skills and job requirements
   *
   * @param unmatchedResumeSkills - Resume skills that didn't have direct matches
   * @param unmatchedJobSkills - Job skills that didn't have direct matches
   * @param options - Configuration options for the analysis
   * @returns Promise resolving to array of transferable skill matches
   */
  async analyzeTransferableSkills(
    unmatchedResumeSkills: SingleResumeSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SingleResumeSkillAnalysisOptions = {}
  ): Promise<SingleResumeTransferableSkillMatch[]> {
    if (!unmatchedJobSkills?.length || !unmatchedResumeSkills.length) {
      return [];
    }

    // Estimate token count to determine processing strategy
    const estimatedTokens = this.estimateTokenCount(
      unmatchedResumeSkills,
      unmatchedJobSkills
    );

    let allMatches: SingleResumeTransferableSkillMatch[] = [];

    try {
      if (estimatedTokens <= this.MAX_SAFE_TOKENS) {
        // Use single-call approach for efficiency
        allMatches = await this.analyzeSingleCall(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          options
        );
      } else {
        // Fallback to batching for large inputs
        console.warn(
          `Large input detected (${estimatedTokens} tokens), falling back to batch processing`
        );
        allMatches = await this.analyzeBatched(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          options
        );
      }

      // Apply confidence threshold and limit
      const filteredMatches = allMatches
        .filter(
          (match) =>
            match.confidenceRating >= (options.confidenceThreshold || 1)
        )
        .sort((a, b) => b.confidenceRating - a.confidenceRating)
        .slice(0, options.maxTransferableSkills || 20);

      return filteredMatches;
    } catch (error) {
      console.warn('Transferable skill analysis failed:', error);
      return [];
    }
  }

  /**
   * Analyzes all skills in a single AI call
   */
  private async analyzeSingleCall(
    unmatchedResumeSkills: SingleResumeSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SingleResumeSkillAnalysisOptions
  ): Promise<SingleResumeTransferableSkillMatch[]> {
    const prompt = ChatPromptTemplate.fromTemplate(`
Analyze the transferability of resume skills to job requirements.
Identify skills that could reasonably transfer to the job requirements, even if they're not direct matches.

Resume Skills:
{resume_skills}

Job Requirements:
{job_skills}

For each potential transferable skill match, provide:
1. Confidence rating (1=low, 2=medium, 3=high)
2. Clear reasoning explaining the transferability

Guidelines:
- Confidence 3: Very similar technologies or direct experience (e.g., React → Vue, MySQL → PostgreSQL)
- Confidence 2: Related domains or transferable concepts (e.g., Java → C#, Frontend → Full-stack)
- Confidence 1: Loosely related or foundational skills (e.g., Python → Data Analysis, Leadership → Team Lead)

Only include matches where there's genuine transferability. Be selective and focus on the strongest connections.

{format_instructions}
    `);

    const parser = StructuredOutputParser.fromZodSchema(
      singleResumeTransferabilityAnalysisSchema
    );
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const resumeSkillsText = unmatchedResumeSkills
      .map(
        (skill) =>
          `- ${skill.name}${
            skill.keywords?.length ? ` (${skill.keywords.join(', ')})` : ''
          }`
      )
      .join('\n');

    const jobSkillsText = unmatchedJobSkills
      .map(
        (skill) =>
          `- ${skill.name}${
            skill.keywords?.length ? ` (${skill.keywords.join(', ')})` : ''
          }`
      )
      .join('\n');

    const operation = chain.invoke({
      resume_skills: resumeSkillsText,
      job_skills: jobSkillsText,
      format_instructions: parser.getFormatInstructions(),
    });

    const result = options.timeoutMs
      ? await withTimeout(
          operation,
          options.timeoutMs,
          'Transferable skill analysis'
        )
      : await operation;

    // Convert AI results to SingleResumeTransferableSkillMatch format
    return result.matches.map((match) => {
      const originalResumeSkill = unmatchedResumeSkills.find(
        (skill) => skill.name === match.resumeSkill
      );

      return {
        jobSkill: match.jobSkill,
        resumeSkill: match.resumeSkill,
        confidenceRating: match.confidenceRating,
        reasoning: match.reasoning,
        source: originalResumeSkill?.source || 'explicit',
        sourceSection: originalResumeSkill?.sourceSection,
      };
    });
  }

  /**
   * Analyzes skills in batches for large inputs
   */
  private async analyzeBatched(
    unmatchedResumeSkills: SingleResumeSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SingleResumeSkillAnalysisOptions
  ): Promise<SingleResumeTransferableSkillMatch[]> {
    const batchSize = 5; // Smaller batches for transferable analysis
    const allMatches: SingleResumeTransferableSkillMatch[] = [];

    // Process job skills in batches
    for (let i = 0; i < unmatchedJobSkills.length; i += batchSize) {
      const jobSkillBatch = unmatchedJobSkills.slice(i, i + batchSize);

      const batchMatches = await this.analyzeSingleCall(
        unmatchedResumeSkills,
        jobSkillBatch,
        options
      );

      allMatches.push(...batchMatches);
    }

    return allMatches;
  }

  /**
   * Estimates token count for the analysis
   */
  private estimateTokenCount(
    resumeSkills: SingleResumeSkill[],
    jobSkills: Job['skills']
  ): number {
    const resumeSkillsText = resumeSkills
      .map((skill) => skill.name + (skill.keywords?.join(' ') || ''))
      .join(' ');

    const jobSkillsText = jobSkills
      .map((skill) => skill.name + (skill.keywords?.join(' ') || ''))
      .join(' ');

    const totalText = resumeSkillsText + jobSkillsText;

    // Rough estimation: 1 token ≈ 4 characters
    // Add overhead for prompt template
    return Math.ceil(totalText.length / 4) + 500;
  }

  /**
   * Identifies missing skills that the resume doesn't have
   */
  identifyMissingSkills(
    jobSkills: Job['skills'],
    directMatches: Array<{ jobSkill: string }>,
    transferableMatches: Array<{ jobSkill: string }>
  ) {
    const matchedJobSkills = new Set([
      ...directMatches.map((match) => match.jobSkill.toLowerCase()),
      ...transferableMatches.map((match) => match.jobSkill.toLowerCase()),
    ]);

    return (jobSkills || [])
      .filter((skill) => !matchedJobSkills.has(skill.name.toLowerCase()))
      .map((skill) => ({
        name: skill.name,
        level: skill.level,
        keywords: skill.keywords,
        category: this.categorizeSkill(skill.name),
      }));
  }

  /**
   * Categorizes a skill for learning recommendations
   */
  private categorizeSkill(skillName: string): string {
    const skill = skillName.toLowerCase();

    if (
      skill.includes('javascript') ||
      skill.includes('typescript') ||
      skill.includes('python') ||
      skill.includes('java') ||
      skill.includes('c#') ||
      skill.includes('go') ||
      skill.includes('rust')
    ) {
      return 'Programming Language';
    }

    if (
      skill.includes('react') ||
      skill.includes('angular') ||
      skill.includes('vue') ||
      skill.includes('svelte')
    ) {
      return 'Frontend Framework';
    }

    if (
      skill.includes('node') ||
      skill.includes('express') ||
      skill.includes('django') ||
      skill.includes('flask') ||
      skill.includes('spring')
    ) {
      return 'Backend Framework';
    }

    if (
      skill.includes('mysql') ||
      skill.includes('postgresql') ||
      skill.includes('mongodb') ||
      skill.includes('redis')
    ) {
      return 'Database';
    }

    if (
      skill.includes('aws') ||
      skill.includes('azure') ||
      skill.includes('gcp') ||
      skill.includes('docker') ||
      skill.includes('kubernetes')
    ) {
      return 'Cloud/DevOps';
    }

    return 'Other';
  }
}
