import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { z } from 'zod';

import type { Job } from '../../../entities/job';
import type {
  ConsolidatedSkill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';

/**
 * Schema for AI-powered transferable skill analysis
 */
export const transferabilityAnalysisSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      confidenceRating: z.union([z.literal(1), z.literal(2), z.literal(3)]),
      reasoning: z.string().max(200),
    })
  ),
});

/**
 * Base class for transferable skill extraction
 */
export abstract class BaseExtractor {
  protected readonly outputParser: StructuredOutputParser<
    typeof transferabilityAnalysisSchema
  >;
  protected readonly promptTemplate: ChatPromptTemplate;

  constructor(protected readonly model: BaseChatModel) {
    this.outputParser = StructuredOutputParser.fromZodSchema(
      transferabilityAnalysisSchema
    );
    this.promptTemplate = this.createTransferabilityPrompt();
  }

  /**
   * Abstract method for extracting transferable skills
   * Must be implemented by subclasses
   */
  abstract extractTransferableSkills(
    unmatchedResumeSkills: ConsolidatedSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]>;

  /**
   * Creates the prompt template for transferable skill analysis
   */
  protected createTransferabilityPrompt(): ChatPromptTemplate {
    return ChatPromptTemplate.fromTemplate(`
You are an expert career counselor analyzing skill transferability between a candidate's resume and job requirements.

Your task is to comprehensively analyze ALL resume skills against ALL job requirements to identify transferable matches. You will receive the complete set of unmatched skills and should find all relevant transferable relationships in a single analysis.

**Analysis Criteria:**

1. **Technology Family Relationships**: Skills in the same technology family (e.g., React vs Vue, MySQL vs PostgreSQL) should have high transferability (confidence 2-3)

2. **Conceptual Similarity**: Skills that share similar concepts or problem-solving approaches (e.g., Java vs C#, AWS vs Azure) should have moderate to high transferability (confidence 2-3)

3. **Domain Knowledge Transfer**: Skills that require similar domain knowledge or thinking patterns (e.g., data analysis tools, testing frameworks) should have moderate transferability (confidence 1-2)

4. **Learning Curve Consideration**: Skills that require significant learning or paradigm shifts should have lower confidence ratings (confidence 1-2)

**Confidence Rating Guidelines:**
- **3 (High)**: Very similar technologies, minimal learning curve, direct transferability
- **2 (Medium)**: Related technologies, moderate learning curve, good transferability with some adaptation
- **1 (Low)**: Conceptually related but requires significant learning, basic transferability

**Complete Resume Skills to Analyze:**
{resumeSkills}

**Complete Job Requirements to Match Against:**
{jobSkills}

**Instructions:**
- Analyze EVERY resume skill against EVERY job requirement
- Identify ALL transferable matches with confidence ratings 1-3
- Provide clear, concise reasoning (max 200 characters) for each match
- Focus on the most relevant and valuable transferable relationships
- Consider the candidate's learning potential and skill adaptation capabilities

{formatInstructions}
`);
  }

  /**
   * Validates a match result from AI analysis
   */
  protected validateMatch(
    match: TransferableSkillMatch,
    options: SkillMatchOptions
  ): boolean {
    // Validate confidence rating
    if (!this.isValidConfidenceRating(match.confidenceRating)) {
      console.warn(
        `Invalid confidence rating: ${match.confidenceRating} for match ${match.resumeSkill} -> ${match.jobSkill}`
      );
      return false;
    }

    // Apply confidence threshold
    if (match.confidenceRating < (options.confidenceThreshold || 1)) {
      return false;
    }

    // Validate reasoning quality
    if (!this.isValidReasoning(match.reasoning)) {
      console.warn(
        `Invalid reasoning for match ${match.resumeSkill} -> ${match.jobSkill}: ${match.reasoning}`
      );
      return false;
    }

    return true;
  }

  /**
   * Validates that confidence rating is within acceptable range
   */
  protected isValidConfidenceRating(rating: unknown): rating is 1 | 2 | 3 {
    return typeof rating === 'number' && [1, 2, 3].includes(rating);
  }

  /**
   * Validates reasoning text quality
   */
  protected isValidReasoning(reasoning: unknown): reasoning is string {
    if (typeof reasoning !== 'string') {
      return false;
    }

    // Check minimum length (should have some meaningful content)
    if (reasoning.trim().length < 10) {
      return false;
    }

    // Check maximum length (as per schema constraint)
    if (reasoning.length > 200) {
      return false;
    }

    // Check for placeholder or generic responses
    const genericResponses = [
      'similar',
      'related',
      'both are',
      'same category',
      'no reason',
      'n/a',
      'none',
    ];

    const lowerReasoning = reasoning.toLowerCase();
    const isGeneric = genericResponses.some(
      (generic) =>
        lowerReasoning === generic || lowerReasoning.startsWith(generic + '.')
    );

    if (isGeneric) {
      return false;
    }

    return true;
  }

  /**
   * Validates and cleans reasoning text
   */
  protected validateAndCleanReasoning(reasoning: string): string {
    // Trim whitespace
    let cleaned = reasoning.trim();

    // Ensure it doesn't exceed maximum length
    if (cleaned.length > 200) {
      cleaned = cleaned.substring(0, 197) + '...';
    }

    // Ensure it starts with a capital letter
    if (cleaned.length > 0 && cleaned[0] !== cleaned[0].toUpperCase()) {
      cleaned = cleaned[0].toUpperCase() + cleaned.slice(1);
    }

    // Ensure it ends with proper punctuation
    if (cleaned.length > 0 && !cleaned.match(/[.!?]$/)) {
      cleaned += '.';
    }

    return cleaned;
  }
}
