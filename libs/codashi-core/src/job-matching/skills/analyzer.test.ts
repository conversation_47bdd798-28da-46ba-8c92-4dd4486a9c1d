import { FakeListChatModel } from '@langchain/core/utils/testing';
import { describe, expect, it } from 'vitest';

import { IsoDate } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { analyzeSkillMatch, SkillMatchError } from './analyzer';

describe('analyzeSkillMatch', () => {
  const mockModel = new FakeListChatModel({
    responses: [
      JSON.stringify({
        matches: [
          {
            resumeSkill: 'JavaScript',
            jobSkill: 'JS',
            isSynonym: true,
            reasoning: 'JavaScript and JS are the same language',
          },
        ],
      }),
      JSON.stringify({
        matches: [
          {
            resumeSkill: 'React',
            jobSkill: 'Vue',
            confidenceRating: 2,
            reasoning: 'Both are frontend frameworks with similar concepts',
          },
        ],
      }),
    ],
  });

  const sampleResume: Resume = {
    $schema: null,
    meta: {
      canonical: null,
      version: null,
      last_modified: null,
      job_description: null,
      created_at: '2024-01-01T00:00:00Z' as IsoDate,
    },
    header: {
      items: [
        {
          name: 'name',
          value: '<PERSON>',
        },
      ],
    },
    sections: [
      {
        name: 'skills',
        items: [
          {
            name: 'JavaScript',
            level: 'Advanced',
            keywords: ['ES6', 'Node.js'],
          },
          {
            name: 'React',
            level: 'Intermediate',
            keywords: ['JSX', 'Hooks'],
          },
        ],
      },
    ],
  };

  const sampleJob: Job = {
    title: 'Frontend Developer',
    company: 'Tech Corp',
    type: 'Full-time',
    date: '2024-01-01T00:00:00Z' as IsoDate,
    description: 'Frontend development role',
    location: null,
    remote: 'Full',
    salary: null,
    experience: null,
    responsibilities: null,
    qualifications: null,
    skills: [
      {
        name: 'JS',
        level: 'Advanced',
        keywords: ['ES6'],
      },
      {
        name: 'Vue',
        level: 'Intermediate',
        keywords: ['Vue 3'],
      },
      {
        name: 'CSS',
        level: 'Intermediate',
        keywords: null,
      },
    ],
    meta: null,
    company_meta: {
      type: null,
      size: null,
      tone: null,
      internal_company_id: null,
    },
    notes: null,
    benefits: null,
  };

  it('should successfully analyze skill matches', async () => {
    const result = await analyzeSkillMatch(
      [sampleResume],
      sampleJob,
      mockModel
    );

    expect(result).toBeDefined();
    expect(result.directMatches).toBeDefined();
    expect(result.transferableSkills).toBeDefined();
    expect(result.missingSkills).toBeDefined();
    expect(result.summary).toBeDefined();

    // Should have summary statistics
    expect(result.summary.totalJobSkills).toBe(3);
    expect(result.summary.coveragePercentage).toBeGreaterThan(0);
  });

  it('should handle empty resume variations', async () => {
    await expect(analyzeSkillMatch([], sampleJob, mockModel)).rejects.toThrow(
      SkillMatchError
    );
  });

  it('should apply default options correctly', async () => {
    const result = await analyzeSkillMatch(
      [sampleResume],
      sampleJob,
      mockModel
    );

    // Default options should not include source resume information
    result.directMatches.forEach((match) => {
      expect(match.sourceResume).toBeUndefined();
    });

    result.transferableSkills.forEach((match) => {
      expect(match.sourceResume).toBeUndefined();
    });
  });

  it('should include source resume information when requested', async () => {
    const result = await analyzeSkillMatch(
      [sampleResume],
      sampleJob,
      mockModel,
      { includeSourceResume: true }
    );

    // Should include source resume information when requested
    if (result.directMatches.length > 0) {
      expect(result.directMatches[0].sourceResume).toBeDefined();
    }
  });

  it('should handle job with no skills', async () => {
    const jobWithoutSkills: Job = {
      ...sampleJob,
      skills: null,
    };

    const result = await analyzeSkillMatch(
      [sampleResume],
      jobWithoutSkills,
      mockModel
    );

    expect(result.directMatches).toHaveLength(0);
    expect(result.transferableSkills).toHaveLength(0);
    expect(result.missingSkills).toHaveLength(0);
    expect(result.summary.totalJobSkills).toBe(0);
    expect(result.summary.coveragePercentage).toBe(0);
  });

  it('should handle multiple resume variations', async () => {
    const secondResume: Resume = {
      ...sampleResume,
      sections: [
        {
          name: 'skills',
          items: [
            {
              name: 'Python',
              level: 'Advanced',
              keywords: ['Django', 'Flask'],
            },
          ],
        },
      ],
    };

    const result = await analyzeSkillMatch(
      [sampleResume, secondResume],
      sampleJob,
      mockModel
    );

    expect(result).toBeDefined();
    expect(result.summary.totalJobSkills).toBe(3);
  });

  it('should apply confidence threshold correctly', async () => {
    const result = await analyzeSkillMatch(
      [sampleResume],
      sampleJob,
      mockModel,
      { confidenceThreshold: 3 }
    );

    // With high confidence threshold, should filter out lower confidence matches
    result.transferableSkills.forEach((match) => {
      expect(match.confidenceRating).toBeGreaterThanOrEqual(3);
    });
  });

  it('should limit transferable skills when maxTransferableSkills is set', async () => {
    const result = await analyzeSkillMatch(
      [sampleResume],
      sampleJob,
      mockModel,
      { maxTransferableSkills: 1 }
    );

    expect(result.transferableSkills.length).toBeLessThanOrEqual(1);
  });
});
