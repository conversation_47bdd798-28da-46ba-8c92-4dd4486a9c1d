import { FakeListChatModel } from '@langchain/core/utils/testing';
import { beforeEach, describe, expect, it } from 'vitest';

import { IsoDate } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { TitleMatchAnalyzer, TitleMatchError } from './analyzer';

describe('TitleMatchAnalyzer', () => {
  let analyzer: TitleMatchAnalyzer;
  let fakeModel: FakeListChatModel;

  beforeEach(() => {
    fakeModel = new FakeListChatModel({
      responses: [],
    });

    analyzer = new TitleMatchAnalyzer(fakeModel);
  });

  const createTestResume = (title: string): Resume => ({
    $schema: null,
    meta: {
      job_description: null,
      created_at: new Date().toISOString() as IsoDate,
      last_modified: null,
      version: null,
      canonical: null,
    },
    header: {
      items: [
        { name: 'name', value: '<PERSON>' },
        { name: 'title', value: title },
        { name: 'email', value: '<EMAIL>' },
      ],
    },
    sections: [],
  });

  const createTestJob = (title: string): Job => ({
    title,
    company: 'Test Company',
    type: 'Full-time',
    date: null,
    description: 'Test job description',
    location: null,
    remote: null,
    salary: null,
    experience: null,
    responsibilities: null,
    qualifications: null,
    skills: null,
    meta: null,
    company_meta: {
      type: null,
      size: null,
      tone: null,
      internal_company_id: null,
    },
    notes: null,
    benefits: null,
  });

  describe('findBestTitleMatch', () => {
    it('should return best title match with valid input', async () => {
      // Arrange
      const resume1 = createTestResume('Senior Software Engineer');
      const resume2 = createTestResume('Software Developer');
      const job = createTestJob('Senior Software Developer');

      // Configure fake model to return expected response
      fakeModel = new FakeListChatModel({
        responses: [
          JSON.stringify({
            rankedTitles: [
              { title: 'Senior Software Engineer', rank: 4 },
              { title: 'Software Developer', rank: 3 },
            ],
            pickedTitle: {
              value: 'Senior Software Engineer',
              reasoning: 'Best match based on seniority and role',
            },
          }),
        ],
      });

      analyzer = new TitleMatchAnalyzer(fakeModel);

      // Act
      const result = await analyzer.findBestTitleMatch([resume1, resume2], job);

      // Assert
      expect(result).toEqual({
        rankedTitles: [
          { title: 'Senior Software Engineer', rank: 4 },
          { title: 'Software Developer', rank: 3 },
        ],
        pickedTitle: {
          value: 'Senior Software Engineer',
          reasoning: 'Best match based on seniority and role',
        },
      });
    });

    it('should throw TitleMatchError when no titles found in resumes', async () => {
      // Arrange
      const resumeWithoutTitle = createTestResume('');
      const job = createTestJob('Software Engineer');

      // Act & Assert
      await expect(
        analyzer.findBestTitleMatch([resumeWithoutTitle], job)
      ).rejects.toThrow(TitleMatchError);
      await expect(
        analyzer.findBestTitleMatch([resumeWithoutTitle], job)
      ).rejects.toHaveProperty('code', 'INVALID_INPUT');
    });

    it('should handle duplicate titles correctly', async () => {
      // Arrange
      const resume1 = createTestResume('Software Engineer');
      const resume2 = createTestResume('Software Engineer'); // Duplicate title
      const job = createTestJob('Software Developer');

      // Configure fake model to return expected response
      fakeModel = new FakeListChatModel({
        responses: [
          JSON.stringify({
            rankedTitles: [{ title: 'Software Engineer', rank: 4 }],
            pickedTitle: {
              value: 'Software Engineer',
              reasoning: 'Exact match with job title',
            },
          }),
        ],
      });

      analyzer = new TitleMatchAnalyzer(fakeModel);

      // Act
      const result = await analyzer.findBestTitleMatch([resume1, resume2], job);

      // Assert
      expect(result).toEqual({
        rankedTitles: [{ title: 'Software Engineer', rank: 4 }],
        pickedTitle: {
          value: 'Software Engineer',
          reasoning: 'Exact match with job title',
        },
      });
    });
  });

  describe('extractResumeTitles', () => {
    it('should extract unique titles from resume variations', () => {
      // Arrange
      const resume1 = createTestResume('Software Engineer');
      const resume2 = createTestResume('Senior Software Engineer');
      const resume3 = createTestResume('Software Engineer'); // Duplicate

      // Act
      const titles = analyzer['extractResumeTitles']([
        resume1,
        resume2,
        resume3,
      ]);

      // Assert
      expect(titles).toEqual(['Software Engineer', 'Senior Software Engineer']);
    });

    it('should return empty array when no titles found', () => {
      // Arrange
      const resumeWithoutTitle = createTestResume('');

      // Act
      const titles = analyzer['extractResumeTitles']([resumeWithoutTitle]);

      // Assert
      expect(titles).toEqual(['']);
    });
  });
});
