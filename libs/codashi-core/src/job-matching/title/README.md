# Title Matching Module

This module provides functionality for analyzing and matching job titles between resume variations and job postings.

## Overview

The title matching module uses AI-powered semantic analysis to determine how well resume job titles align with job posting titles. It provides confidence scores and reasoning for matches, helping to identify the most suitable job title variation.

## Key Features

- **Semantic Similarity Analysis**: Uses AI to understand the semantic relationship between job titles
- **Confidence Scoring**: Provides a confidence score (0-1) for each match
- **Reasoning**: Includes detailed reasoning for each match decision
- **Error Handling**: Comprehensive error handling for various edge cases

## Usage

```typescript
import { TitleMatchAnalyzer } from './analyzer';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';

// Initialize with a LangChain BaseChatModel
const analyzer = new TitleMatchAnalyzer(chatModel);

// Analyze title matches
const bestMatch = await analyzer.findBestTitleMatch(
  resumeVariations,
  jobPosting,
  { timeoutMs: 5000 } // Optional timeout
);

console.log(`Best match: ${bestMatch.resumeTitle}`);
console.log(`Confidence: ${bestMatch.confidence}`);
console.log(`Reasoning: ${bestMatch.reasoning}`);
```

## API Reference

### `TitleMatchAnalyzer` Class

#### Constructor

```typescript
constructor(model: BaseChatModel)
```

#### Methods

##### `findBestTitleMatch`

```typescript
async findBestTitleMatch(
  resumeVariations: Resume[],
  job: Job,
  options?: { timeoutMs?: number }
): Promise<TitleMatch>
```

Finds the best matching job title from resume variations.

**Parameters:**

- `resumeVariations`: Array of resume variations to analyze
- `job`: Job posting to match against
- `options`: Optional configuration
  - `timeoutMs`: Timeout in milliseconds for the analysis

**Returns:**

- `TitleMatch` object containing:
  - `resumeTitle`: The best matching resume title
  - `jobTitle`: The job title from the posting
  - `confidence`: Confidence score (0-1)
  - `reasoning`: Explanation for the match decision

**Throws:**

- `TitleMatchError` with appropriate error code if analysis fails

### Types

#### `TitleMatch`

```typescript
interface TitleMatch {
  resumeTitle: string;
  jobTitle: string;
  confidence: number; // 0-1 scale
  reasoning: string;
}
```

#### `TitleMatchError`

```typescript
class TitleMatchError extends Error {
  constructor(message: string, public readonly code: 'AI_UNAVAILABLE' | 'TIMEOUT' | 'INVALID_INPUT' | 'NO_MATCHES') {
    super(message);
    this.name = 'TitleMatchError';
  }
}
```

## Examples

### Basic Usage

```typescript
const analyzer = new TitleMatchAnalyzer(chatModel);

const resumes = [
  // Resume variations...
];

const job = {
  title: 'Senior Software Engineer',
  // ... other job properties
};

const bestMatch = await analyzer.findBestTitleMatch(resumes, job);
console.log(`Best match: ${bestMatch.resumeTitle}`);
```

### With Timeout

```typescript
const bestMatch = await analyzer.findBestTitleMatch(
  resumes,
  job,
  { timeoutMs: 3000 } // 3 second timeout
);
```

## Error Handling

The module provides comprehensive error handling through the `TitleMatchError` class. Common error codes include:

- `AI_UNAVAILABLE`: When the AI model is unavailable
- `TIMEOUT`: When the analysis times out
- `INVALID_INPUT`: When input validation fails
- `NO_MATCHES`: When no suitable matches are found

Always wrap calls to `findBestTitleMatch` in try-catch blocks to handle these errors appropriately.
