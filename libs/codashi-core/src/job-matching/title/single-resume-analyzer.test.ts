import { FakeListChatModel } from '@langchain/core/utils/testing';
import { beforeEach, describe, expect, it } from 'vitest';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  analyzeSingleResumeTitle,
  extractSeniorityLevel,
  getTitleSimilarityScore,
} from './single-resume-analyzer';
import { SingleResumeTitleMatchError } from './single-resume-types';

describe('analyzeSingleResumeTitle', () => {
  let mockModel: FakeListChatModel;
  let mockJob: Job;
  let mockResume: Resume;

  beforeEach(() => {
    mockModel = new FakeListChatModel({
      responses: [
        JSON.stringify({
          confidence: 0.85,
          reasoning:
            'Strong match between Software Engineer and Software Developer roles',
          isGoodMatch: true,
          suggestedTitle: 'Senior Software Developer',
        }),
      ],
    });

    mockJob = {
      title: 'Senior Software Developer',
      skills: [
        { name: 'JavaScript', keywords: ['JS'], category: 'Programming' },
        { name: 'React', keywords: ['ReactJS'], category: 'Frontend' },
      ],
      qualifications: ["Bachelor's degree", '3+ years experience'],
    } as Job;

    mockResume = {
      header: {
        name: 'Resume Header',
        items: [
          { name: 'title', value: 'Software Engineer' },
          { name: 'name', value: 'John Doe' },
          { name: 'email', value: '<EMAIL>' },
        ],
      },
      sections: [],
    } as Resume;
  });

  it('should analyze title match successfully', async () => {
    const result = await analyzeSingleResumeTitle(
      mockResume,
      mockJob,
      mockModel,
      { confidenceThreshold: 0.9 } // Set threshold higher than 0.85 to ensure suggestion is included
    );

    expect(result.originalTitle).toBe('Software Engineer');
    expect(result.jobTitle).toBe('Senior Software Developer');
    expect(result.confidence).toBe(0.85);
    expect(result.reasoning).toContain('Strong match');
    expect(result.summary.isGoodMatch).toBe(true);
    expect(result.summary.confidenceLevel).toBe('high');
    expect(result.summary.hasImprovement).toBe(true);
    expect(result.suggestedTitle).toBe('Senior Software Developer');
  });

  it('should handle resume without title', async () => {
    const resumeWithoutTitle = {
      header: {
        name: 'Resume Header',
        items: [
          { name: 'name', value: 'John Doe' },
          { name: 'email', value: '<EMAIL>' },
        ],
      },
      sections: [],
    } as Resume;

    await expect(
      analyzeSingleResumeTitle(resumeWithoutTitle, mockJob, mockModel)
    ).rejects.toThrow(SingleResumeTitleMatchError);
  });

  it('should handle empty title', async () => {
    const resumeWithEmptyTitle = {
      header: {
        name: 'Resume Header',
        items: [
          { name: 'title', value: '' },
          { name: 'name', value: 'John Doe' },
        ],
      },
      sections: [],
    } as Resume;

    await expect(
      analyzeSingleResumeTitle(resumeWithEmptyTitle, mockJob, mockModel)
    ).rejects.toThrow(SingleResumeTitleMatchError);
  });

  it('should respect suggestImprovements option', async () => {
    const result = await analyzeSingleResumeTitle(
      mockResume,
      mockJob,
      mockModel,
      { suggestImprovements: false }
    );

    expect(result.suggestedTitle).toBeUndefined();
    expect(result.summary.hasImprovement).toBe(false);
  });

  it('should respect confidence threshold', async () => {
    const result = await analyzeSingleResumeTitle(
      mockResume,
      mockJob,
      mockModel,
      { confidenceThreshold: 0.9 }
    );

    // Since confidence is 0.85, which is below 0.9 threshold, suggestion should be included
    expect(result.suggestedTitle).toBe('Senior Software Developer');
  });

  it('should validate inputs', async () => {
    await expect(
      analyzeSingleResumeTitle(null as any, mockJob, mockModel)
    ).rejects.toThrow(SingleResumeTitleMatchError);

    await expect(
      analyzeSingleResumeTitle(mockResume, null as any, mockModel)
    ).rejects.toThrow(SingleResumeTitleMatchError);

    await expect(
      analyzeSingleResumeTitle(mockResume, mockJob, null as any)
    ).rejects.toThrow(SingleResumeTitleMatchError);
  });

  it('should handle job without title', async () => {
    const jobWithoutTitle = { ...mockJob, title: '' };

    await expect(
      analyzeSingleResumeTitle(mockResume, jobWithoutTitle, mockModel)
    ).rejects.toThrow(SingleResumeTitleMatchError);
  });
});

describe('getTitleSimilarityScore', () => {
  it('should calculate similarity correctly', () => {
    expect(
      getTitleSimilarityScore('Software Engineer', 'Software Developer')
    ).toBeGreaterThanOrEqual(0.5);
    expect(
      getTitleSimilarityScore('Software Engineer', 'Marketing Manager')
    ).toBeLessThan(0.5);
    expect(
      getTitleSimilarityScore('Senior Software Engineer', 'Software Engineer')
    ).toBeGreaterThan(0.5);
  });

  it('should handle empty strings', () => {
    expect(getTitleSimilarityScore('', 'Software Engineer')).toBe(0);
    expect(getTitleSimilarityScore('Software Engineer', '')).toBe(0);
    expect(getTitleSimilarityScore('', '')).toBe(0);
  });
});

describe('extractSeniorityLevel', () => {
  it('should extract seniority levels correctly', () => {
    expect(extractSeniorityLevel('Junior Software Engineer')).toBe('junior');
    expect(extractSeniorityLevel('Senior Software Engineer')).toBe('senior');
    expect(extractSeniorityLevel('Lead Software Engineer')).toBe('lead');
    expect(extractSeniorityLevel('Software Engineer')).toBe('mid');
    expect(extractSeniorityLevel('Principal Engineer')).toBe('lead');
    expect(extractSeniorityLevel('Staff Engineer')).toBe('lead');
  });

  it('should handle case variations', () => {
    expect(extractSeniorityLevel('SENIOR SOFTWARE ENGINEER')).toBe('senior');
    expect(extractSeniorityLevel('junior developer')).toBe('junior');
  });
});
