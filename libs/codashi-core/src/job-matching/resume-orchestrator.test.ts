import type { IsoDate } from '@awe/core';
import { FakeListChatModel } from '@langchain/core/utils/testing';
import { beforeEach, describe, expect, it } from 'vitest';

import type { Job } from '../entities/job';
import type { Resume } from '../entities/resume';
import { analyzeMultipleResumes, analyzeResume } from './resume-orchestrator';

describe('Resume Orchestrator', () => {
  let mockModel: FakeListChatModel;
  let mockJob: Job;
  let mockResume: Resume;

  beforeEach(() => {
    mockModel = new FakeListChatModel({
      responses: [
        // Experience scoring response
        JSON.stringify({
          scores: [
            {
              experienceId: 'exp1',
              relevanceScore: 4,
              reasoning: 'Highly relevant experience',
            },
          ],
        }),
        // Skills extraction response
        JSON.stringify({
          skills: ['JavaScript', 'React', 'Node.js'],
        }),
        // Transferable skills analysis response
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue.js',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Similar frontend frameworks',
            },
          ],
        }),
        // Title analysis response
        JSON.stringify({
          confidence: 0.85,
          reasoning: 'Strong match between titles',
          isGoodMatch: true,
          suggestedTitle: 'Senior Software Developer',
        }),
      ],
    });

    mockJob = {
      title: 'Senior Software Developer',
      description: 'We are looking for a senior software developer...',
      skills: [
        { name: 'JavaScript', keywords: ['JS'], category: 'Programming' },
        { name: 'React', keywords: ['ReactJS'], category: 'Frontend' },
        { name: 'Node.js', keywords: ['NodeJS'], category: 'Backend' },
      ],
      qualifications: ["Bachelor's degree", '5+ years experience'],
      company: 'Tech Corp',
      location: 'San Francisco, CA',
      salary_range: null,
      employment_type: 'full-time',
      remote_policy: 'hybrid',
      posted_date: '2024-01-01' as IsoDate,
      application_deadline: null,
      benefits: null,
      notes: null,
    };

    mockResume = {
      $schema: null,
      meta: {
        canonical: null,
        version: null,
        last_modified: null,
        job_description: null,
        created_at: '2024-01-01' as IsoDate,
      },
      header: {
        items: [
          { name: 'name', value: 'John Doe' },
          { name: 'title', value: 'Software Engineer' },
          { name: 'email', value: '<EMAIL>' },
        ],
      },
      sections: [
        {
          name: 'work',
          items: [
            {
              name: 'Tech Startup',
              position: 'Frontend Developer',
              start_date: '2020-01-01' as IsoDate,
              end_date: '2023-12-31' as IsoDate,
              summary: 'Developed React applications',
              description: 'Built modern web applications using React and JavaScript',
              highlights: ['Improved performance by 30%', 'Led team of 3 developers'],
              location: 'San Francisco, CA',
              url: null,
            },
          ],
        },
        {
          name: 'skills',
          items: [
            { name: 'JavaScript', level: 'Expert', keywords: ['ES6', 'TypeScript'] },
            { name: 'React', level: 'Advanced', keywords: ['Hooks', 'Redux'] },
            { name: 'Vue.js', level: 'Intermediate', keywords: ['Vuex'] },
          ],
        },
      ],
    };
  });

  describe('analyzeResume', () => {
    it('should analyze a single resume against a job posting', async () => {
      const result = await analyzeResume(mockResume, mockJob, mockModel);

      expect(result).toHaveProperty('experience');
      expect(result).toHaveProperty('skills');
      expect(result).toHaveProperty('title');
      expect(result).toHaveProperty('overallSummary');

      // Check experience analysis
      expect(result.experience.experiences).toHaveLength(1);
      expect(result.experience.experiences[0].name).toBe('Tech Startup');
      expect(result.experience.experiences[0].position).toBe('Frontend Developer');

      // Check skills analysis
      expect(result.skills.resumeSkills.length).toBeGreaterThan(0);
      expect(result.skills.directMatches.length).toBeGreaterThan(0);

      // Check title analysis
      expect(result.title.originalTitle).toBe('Software Engineer');
      expect(result.title.jobTitle).toBe('Senior Software Developer');
      expect(result.title.confidence).toBe(0.85);

      // Check overall summary
      expect(result.overallSummary).toHaveProperty('totalScore');
      expect(result.overallSummary).toHaveProperty('matchQuality');
      expect(result.overallSummary).toHaveProperty('isRecommended');
      expect(result.overallSummary.totalScore).toBeGreaterThan(0);
    });

    it('should handle resume with no work experience', async () => {
      const resumeWithoutWork = {
        ...mockResume,
        sections: [
          {
            name: 'skills',
            items: [
              { name: 'JavaScript', level: 'Expert', keywords: ['ES6'] },
            ],
          },
        ],
      } as Resume;

      const result = await analyzeResume(resumeWithoutWork, mockJob, mockModel);

      expect(result.experience.experiences).toHaveLength(0);
      expect(result.experience.summary.totalExperiences).toBe(0);
      expect(result.overallSummary.totalScore).toBeGreaterThanOrEqual(0);
    });

    it('should handle resume with no skills', async () => {
      const resumeWithoutSkills = {
        ...mockResume,
        sections: [
          {
            name: 'work',
            items: [
              {
                name: 'Tech Company',
                position: 'Developer',
                start_date: '2020-01-01' as IsoDate,
                end_date: null,
                summary: 'Developed applications',
                description: null,
                highlights: null,
                location: null,
                url: null,
              },
            ],
          },
        ],
      } as Resume;

      const result = await analyzeResume(
        resumeWithoutSkills,
        mockJob,
        mockModel
      );

      expect(result.skills.resumeSkills).toHaveLength(0);
      expect(result.skills.directMatches).toHaveLength(0);
      expect(result.overallSummary.totalScore).toBeGreaterThanOrEqual(0);
    });

    it('should validate required inputs', async () => {
      await expect(
        analyzeResume(null as any, mockJob, mockModel)
      ).rejects.toThrow('Resume is required');

      await expect(
        analyzeResume(mockResume, null as any, mockModel)
      ).rejects.toThrow('Job is required');

      await expect(
        analyzeResume(mockResume, mockJob, null as any)
      ).rejects.toThrow('Model is required');
    });

    it('should respect analysis options', async () => {
      const options = {
        experience: { includeImprovements: false },
        skills: { confidenceThreshold: 3 as const },
        title: { suggestImprovements: false },
        timeoutMs: 30000,
      };

      const result = await analyzeResume(
        mockResume,
        mockJob,
        mockModel,
        options
      );

      expect(result.experience.improvements).toHaveLength(0);
      expect(result.title.suggestedTitle).toBeUndefined();
    });
  });

  describe('analyzeMultipleResumes', () => {
    it('should analyze multiple resumes sequentially', async () => {
      const resume2 = {
        ...mockResume,
        header: {
          items: [
            { name: 'name', value: 'Jane Smith' },
            { name: 'title', value: 'Full Stack Developer' },
            { name: 'email', value: '<EMAIL>' },
          ],
        },
      };

      const results = await analyzeMultipleResumes(
        [mockResume, resume2],
        mockJob,
        mockModel
      );

      expect(results).toHaveLength(2);
      expect(results[0].title.originalTitle).toBe('Software Engineer');
      expect(results[1].title.originalTitle).toBe('Full Stack Developer');

      // Each result should have complete analysis
      results.forEach((result) => {
        expect(result).toHaveProperty('experience');
        expect(result).toHaveProperty('skills');
        expect(result).toHaveProperty('title');
        expect(result).toHaveProperty('overallSummary');
      });
    });

    it('should maintain resume order in results', async () => {
      const resume2 = { ...mockResume };
      const resume3 = { ...mockResume };

      const results = await analyzeMultipleResumes(
        [mockResume, resume2, resume3],
        mockJob,
        mockModel
      );

      expect(results).toHaveLength(3);
      // Results should be in the same order as input
      expect(results[0].title.originalTitle).toBe('Software Engineer');
      expect(results[1].title.originalTitle).toBe('Software Engineer');
      expect(results[2].title.originalTitle).toBe('Software Engineer');
    });

    it('should handle empty resumes array', async () => {
      await expect(
        analyzeMultipleResumes([], mockJob, mockModel)
      ).rejects.toThrow('At least one resume is required');
    });

    it('should handle null or undefined resumes array', async () => {
      await expect(
        analyzeMultipleResumes(null as any, mockJob, mockModel)
      ).rejects.toThrow('At least one resume is required');

      await expect(
        analyzeMultipleResumes(undefined as any, mockJob, mockModel)
      ).rejects.toThrow('At least one resume is required');
    });

    it('should propagate analysis options to all resumes', async () => {
      const options = {
        experience: { includeImprovements: false },
        skills: { confidenceThreshold: 3 as const },
      };

      const results = await analyzeMultipleResumes(
        [mockResume, mockResume],
        mockJob,
        mockModel,
        options
      );

      results.forEach((result) => {
        expect(result.experience.improvements).toHaveLength(0);
      });
    });

    it('should handle individual resume analysis failures gracefully', async () => {
      // Create a model that will fail on the second call
      const failingModel = new FakeListChatModel({
        responses: [
          // First resume - success responses
          JSON.stringify({ scores: [{ experienceId: 'exp1', relevanceScore: 4, reasoning: 'Good' }] }),
          JSON.stringify({ skills: ['JavaScript'] }),
          JSON.stringify({ matches: [] }),
          JSON.stringify({ confidence: 0.8, reasoning: 'Good match', isGoodMatch: true }),
          // Second resume - will cause failure by not providing enough responses
        ],
      });

      // This should fail when processing the second resume
      await expect(
        analyzeMultipleResumes([mockResume, mockResume], mockJob, failingModel)
      ).rejects.toThrow();
    });
  });
});
