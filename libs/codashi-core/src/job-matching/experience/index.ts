/**
 * Experience Analyzer for Job Matching
 *
 * This module provides comprehensive work experience analysis for job matching,
 * including consolidation, relevance scoring, and content improvement suggestions.
 *
 * @example
 * ```typescript
 * import { analyzeExperienceMatch } from '@awe/codashi-core/job-matching/experience';
 *
 * const analysis = await analyzeExperienceMatch(
 *   resumeVariations,
 *   jobPosting,
 *   llmModel,
 *   {
 *     includeImprovements: true,
 *     batchSize: 5,
 *     maxImprovements: 3
 *   }
 * );
 *
 * console.log(`Found ${analysis.summary.totalExperiences} work experiences`);
 * console.log(`Average relevance score: ${analysis.summary.averageRelevanceScore}`);
 * console.log(`Improvement suggestions: ${analysis.summary.improvementsProposed}`);
 * ```
 */

// Main analyzer functions
export { analyzeExperienceMatch } from './analyzer';
export { analyzeSingleResumeExperience } from './single-resume-analyzer';

// Result reduction functions
export {
  getExperiencesByRelevance,
  getTopExperiences,
  reduceExperienceAnalyses,
} from './result-reducer';

// Core classes
export { ExperienceImprover } from './experience-improver';
export { ExperienceScorer } from './experience-scorer';

// Single resume classes
export { SingleResumeExperienceExtractor } from './single-resume-extractor';
export { SingleResumeExperienceImprover } from './single-resume-improver';
export { SingleResumeExperienceScorer } from './single-resume-scorer';

// Types and interfaces
export type {
  ConsolidatedWorkExperience,
  ExperienceAnalysis,
  ExperienceAnalysisOptions,
  ExperienceImprovementResult,
  ExperienceScoreResult,
  WorkExperienceImprovement,
  WorkExperienceScore,
} from './types';

// Single resume types
export type {
  SingleResumeExperienceAnalysis,
  SingleResumeExperienceAnalysisOptions,
  SingleResumeWorkExperience,
  SingleResumeWorkExperienceImprovement,
  SingleResumeWorkExperienceScore,
} from './single-resume-types';

// Prompt components for modular usage
export * from './prompt-components';

// Error classes
export { ExperienceMatchError } from './types';

// Utility functions
export {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
export {
  createDiffPreview,
  estimateExperienceTokens,
  generateExperienceId,
  hasDateOverlap,
  normalizeCompanyName,
  normalizePositionTitle,
} from './utils';
