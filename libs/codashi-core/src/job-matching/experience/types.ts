import { z } from 'zod';
import { ResumeEntity } from '../../entities/resume';

/**
 * Represents a consolidated work experience entry from multiple resume variations
 */
export interface ConsolidatedWorkExperience extends ResumeEntity<'work'> {
  // artificially generated
  id: string;
  /** Which resume variations this experience came from */
  sourceResumes: number[];
}

/**
 * Represents a work experience with its relevance score
 */
export interface WorkExperienceScore {
  /** The work experience being scored */
  experience: ConsolidatedWorkExperience;
  /** Relevance score from 1-5 (5 being most relevant) */
  relevanceScore: number;
  /** AI reasoning for the assigned score */
  reasoning: string;
}

/**
 * Represents a suggested improvement to work experience content
 */
export interface WorkExperienceImprovement {
  /** ID of the experience being improved */
  experienceId: string;
  /** Which field is being improved */
  field: 'summary' | 'highlights' | 'description';
  /** Original content */
  originalContent: string;
  /** Improved content suggestion */
  improvedContent: string;
  /** AI reasoning for the improvement */
  reasoning: string;
  /** Simple diff preview showing changes */
  diffPreview: string;
}

/**
 * Configuration options for experience analysis
 */
export interface ExperienceAnalysisOptions {
  /** Whether to include source resume information in results */
  includeSourceResume?: boolean;
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of experiences per call) */
  batchSize?: number;
  /** Whether to include content improvement suggestions */
  includeImprovements?: boolean;
  /** Maximum number of improvements to suggest */
  maxImprovements?: number;
}

/**
 * Complete analysis result for work experience matching
 */
export interface ExperienceAnalysis {
  /** All consolidated work experiences */
  consolidatedExperiences: ConsolidatedWorkExperience[];
  /** Work experiences with relevance scores */
  scoredExperiences: WorkExperienceScore[];
  /** Suggested content improvements */
  improvements: WorkExperienceImprovement[];
  /** Summary statistics */
  summary: {
    /** Total number of work experiences */
    totalExperiences: number;
    /** Average relevance score across all experiences */
    averageRelevanceScore: number;
    /** Number of highly relevant experiences (score >= 4) */
    highRelevanceCount: number;
    /** Number of improvement suggestions provided */
    improvementsProposed: number;
  };
}

/**
 * Error class for experience matching operations
 */
export class ExperienceMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_EXPERIENCES'
  ) {
    super(message);
    this.name = 'ExperienceMatchError';
  }
}

/**
 * Schema for AI-powered experience scoring results
 */
export const experienceScoreSchema = z.object({
  scores: z.array(
    z.object({
      experienceId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

/**
 * Schema for AI-powered experience improvement suggestions
 */
export const experienceImprovementSchema = z.object({
  hasImprovement: z.boolean(),
  improvedContent: z.string().optional(),
  reasoning: z.string().max(300).optional(),
});

/**
 * Type definitions for Zod schema inference
 */
export type ExperienceScoreResult = z.infer<typeof experienceScoreSchema>;
export type ExperienceImprovementResult = z.infer<
  typeof experienceImprovementSchema
>;
