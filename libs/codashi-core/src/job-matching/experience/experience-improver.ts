import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import type {
  ConsolidatedWorkExperience,
  ExperienceAnalysisOptions,
  WorkExperienceImprovement,
} from './types';
import { experienceImprovementSchema } from './types';
import { createDiffPreview } from './utils';

/**
 * Suggests improvements to work experience content to better align with target jobs.
 *
 * This class uses AI to analyze work experiences and suggest content improvements
 * for summary, highlights, and description fields to make them more compelling
 * and relevant to the target job.
 */
export class ExperienceImprover {
  constructor(private model: BaseChatModel) {}

  /**
   * Suggests improvements for work experience content
   *
   * @param experiences - Array of consolidated work experiences to improve
   * @param job - Job posting to optimize content for
   * @param options - Configuration options
   * @returns Promise resolving to array of improvement suggestions
   */
  async improveExperiences(
    experiences: ConsolidatedWorkExperience[],
    job: Job,
    options: ExperienceAnalysisOptions = {}
  ): Promise<WorkExperienceImprovement[]> {
    if (!experiences.length || !options.includeImprovements) {
      return [];
    }

    const maxImprovements = options.maxImprovements || 10;
    const timeoutMs = options.timeoutMs || 30000;
    const limitedExperiences = experiences.slice(0, maxImprovements);

    try {
      const allImprovements: WorkExperienceImprovement[] = [];

      // Process experiences one at a time for better quality
      for (const experience of limitedExperiences) {
        const improvements = await this.improveExperience(
          experience,
          job,
          timeoutMs
        );
        allImprovements.push(...improvements);
      }

      return allImprovements;
    } catch (error) {
      if (isAIUnavailableError(error) || isTimeoutError(error)) {
        // Return empty array if AI is unavailable
        return [];
      }
      throw error;
    }
  }

  /**
   * Improves a single work experience
   */
  private async improveExperience(
    experience: ConsolidatedWorkExperience,
    job: Job,
    timeoutMs: number
  ): Promise<WorkExperienceImprovement[]> {
    const improvements: WorkExperienceImprovement[] = [];

    // Improve summary if it exists
    if (experience.summary) {
      const summaryImprovement = await this.improveField(
        experience,
        'summary',
        experience.summary,
        job,
        timeoutMs
      );
      if (summaryImprovement) {
        improvements.push(summaryImprovement);
      }
    }

    // Improve highlights if they exist
    if (experience.highlights && experience.highlights.length > 0) {
      const highlightsImprovement = await this.improveField(
        experience,
        'highlights',
        experience.highlights.join('\n• '),
        job,
        timeoutMs
      );
      if (highlightsImprovement) {
        improvements.push(highlightsImprovement);
      }
    }

    // Improve description if it exists
    if (experience.description) {
      const descriptionImprovement = await this.improveField(
        experience,
        'description',
        experience.description,
        job,
        timeoutMs
      );
      if (descriptionImprovement) {
        improvements.push(descriptionImprovement);
      }
    }

    return improvements;
  }

  /**
   * Improves a specific field of a work experience
   */
  private async improveField(
    experience: ConsolidatedWorkExperience,
    field: 'summary' | 'highlights' | 'description',
    content: string,
    job: Job,
    timeoutMs: number
  ): Promise<WorkExperienceImprovement | null> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are an expert resume writer helping to optimize work experience content for a specific job application.

      Target Job:
      Title: {jobTitle}
      Description: {jobDescription}
      Required Skills: {jobSkills}
      Required Qualifications: {jobQualifications}

      Work Experience Context:
      Company: {company}
      Position: {position}
      Duration: {duration}

      Current {fieldName} Content:
      {currentContent}

      Please analyze this {fieldName} and determine if it can be improved to better align with the target job. 

      Guidelines for improvements:
      - Make content more relevant to the target job requirements
      - Add quantifiable achievements where possible (numbers, percentages, metrics)
      - Use stronger action verbs and impact-focused language
      - Highlight transferable skills that match job requirements
      - Keep the original voice and style but enhance clarity and impact
      - Ensure accuracy - don't add false information
      - For highlights: format as bullet points starting with strong action verbs

      If the content is already well-optimized, set hasImprovement to false.
      If improvements are possible, provide the enhanced version with reasoning.

      {format_instructions}
    `);

    const parser = StructuredOutputParser.fromZodSchema(
      experienceImprovementSchema
    );
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const result = await withTimeout(
      chain.invoke({
        jobTitle: job.title || 'Not specified',
        jobDescription: job.description || 'Not specified',
        jobSkills: this.formatJobSkills(job),
        jobQualifications: this.formatJobQualifications(job),
        company: experience.name,
        position: experience.position,
        duration: `${experience.start_date} to ${
          experience.end_date || 'Present'
        }`,
        fieldName: field,
        currentContent: content,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Experience improvement'
    );

    if (!result.hasImprovement || !result.improvedContent) {
      return null;
    }

    return {
      experienceId: experience.id,
      field,
      originalContent: content,
      improvedContent: result.improvedContent,
      reasoning: result.reasoning || 'No specific reasoning provided',
      diffPreview: createDiffPreview(content, result.improvedContent),
    };
  }

  /**
   * Formats job skills for the prompt
   */
  private formatJobSkills(job: Job): string {
    if (!job.skills || job.skills.length === 0) {
      return 'Not specified';
    }

    return job.skills
      .map((skill) => {
        let formatted = skill.name;
        if (skill.level) {
          formatted += ` (${skill.level})`;
        }
        return formatted;
      })
      .join(', ');
  }

  /**
   * Formats job qualifications for the prompt
   */
  private formatJobQualifications(job: Job): string {
    if (!job.qualifications || job.qualifications.length === 0) {
      return 'Not specified';
    }

    return job.qualifications.join(', ');
  }
}
