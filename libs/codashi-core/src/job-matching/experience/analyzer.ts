import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { isAIUnavailableError, isTimeoutError } from '../../utils/common-utils';
import type { ExperienceAnalysis, ExperienceAnalysisOptions } from './types';
import { ExperienceMatchError } from './types';

// New single-resume architecture imports
import { reduceExperienceAnalyses } from './result-reducer';
import { analyzeSingleResumeExperience } from './single-resume-analyzer';

/**
 * Analyzes work experience matches between multiple resume variations and a job posting.
 *
 * This function consolidates work experiences from all resume variations, scores them
 * based on relevance to the target job, and optionally suggests content improvements
 * to make experiences more compelling and relevant.
 *
 * @param resumeVariations - Array of resume variations for the same person
 * @param job - Job posting to match against
 * @param model - Lang<PERSON><PERSON>n BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive work experience analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeExperienceMatch(
 *   [resume1, resume2],
 *   jobPosting,
 *   mistralModel,
 *   {
 *     includeImprovements: true,
 *     batchSize: 5,
 *     maxImprovements: 3
 *   }
 * );
 *
 * console.log(`Total experiences: ${analysis.summary.totalExperiences}`);
 * console.log(`Average relevance: ${analysis.summary.averageRelevanceScore}`);
 * console.log(`Improvements suggested: ${analysis.summary.improvementsProposed}`);
 * ```
 */
export async function analyzeExperienceMatch(
  resumeVariations: [Resume, ...Resume[]],
  job: Job,
  model: BaseChatModel,
  options: ExperienceAnalysisOptions = {}
): Promise<ExperienceAnalysis> {
  const finalOptions = {
    includeSourceResume: true,
    batchSize: 8,
    timeoutMs: 30000, // 30 seconds default timeout
    includeImprovements: true,
    maxImprovements: 5,
    ...options,
  } satisfies ExperienceAnalysisOptions;

  try {
    // NEW ARCHITECTURE: Use single-resume analysis for each resume
    // then reduce the results into the consolidated format

    // Convert options to single-resume format
    const singleResumeOptions = {
      includeImprovements: finalOptions.includeImprovements,
      batchSize: finalOptions.batchSize,
      timeoutMs: finalOptions.timeoutMs,
      maxImprovements: finalOptions.maxImprovements,
    };

    // Analyze each resume individually
    const singleResumeAnalyses = await Promise.all(
      resumeVariations.map((resume) =>
        analyzeSingleResumeExperience(resume, job, model, singleResumeOptions)
      )
    );

    // Reduce the individual analyses into the consolidated format
    const consolidatedResult = reduceExperienceAnalyses(singleResumeAnalyses);

    return consolidatedResult;
  } catch (error) {
    // Handle timeout errors
    if (isTimeoutError(error)) {
      throw new ExperienceMatchError(
        'Experience analysis timed out. Try reducing the number of resume variations or increasing the timeout.',
        'TIMEOUT'
      );
    }

    // Handle AI unavailability
    if (isAIUnavailableError(error)) {
      throw new ExperienceMatchError(
        'AI model is unavailable for experience analysis',
        'AI_UNAVAILABLE'
      );
    }

    // Handle unexpected errors
    throw new ExperienceMatchError(
      `Unexpected error during experience analysis: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      'INVALID_INPUT'
    );
  }
}
