import { FakeListChatModel } from '@langchain/core/utils/testing';
import { describe, expect, it } from 'vitest';

import { IsoDate } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { analyzeSingleResumeExperience } from './single-resume-analyzer';

describe('analyzeSingleResumeExperience', () => {
  // Create a mock model that provides responses for scoring
  const createMockModel = () =>
    new FakeListChatModel({
      responses: [
        // Experience scoring responses
        JSON.stringify({
          scores: [
            {
              experienceId: 'acme-corp-software-engineer-2020-01-01',
              relevanceScore: 4,
              reasoning:
                'Strong React experience aligns well with job requirements',
            },
          ],
        }),
        JSON.stringify({
          scores: [
            {
              experienceId: 'new-corp-senior-developer-2022-01-01',
              relevanceScore: 4,
              reasoning: 'Recent senior role with relevant experience',
            },
            {
              experienceId: 'mid-corp-developer-2020-01-01',
              relevanceScore: 3,
              reasoning: 'Good development experience',
            },
            {
              experienceId: 'old-corp-junior-developer-2018-01-01',
              relevanceScore: 2,
              reasoning: 'Earlier career experience',
            },
          ],
        }),
        JSON.stringify({
          scores: [
            {
              experienceId: 'acme-corp-software-engineer-2020-01-01',
              relevanceScore: 3,
              reasoning: 'Test reasoning',
            },
            {
              experienceId: 'beta-inc-frontend-developer-2021-01-01',
              relevanceScore: 4,
              reasoning: 'Test reasoning',
            },
          ],
        }),
      ],
    });

  const createMockJob = (): Job => ({
    title: 'Senior Software Engineer',
    company: 'Tech Corp',
    type: 'Full-time',
    date: null,
    description:
      'We are looking for a senior software engineer with React and Node.js experience.',
    location: null,
    remote: null,
    salary: null,
    experience: null,
    responsibilities: null,
    qualifications: [
      '5+ years of software development experience',
      'Experience with modern web frameworks',
    ],
    skills: [
      { name: 'React', level: 'Expert', keywords: [] },
      { name: 'Node.js', level: 'Advanced', keywords: [] },
      { name: 'TypeScript', level: 'Intermediate', keywords: [] },
    ],
    meta: {
      canonical: null,
      version: null,
      last_modified: null,
    },
    company_meta: {
      type: null,
      size: null,
      tone: null,
      internal_company_id: null,
    },
    notes: null,
    benefits: null,
  });

  const createMockResume = (
    workExperiences: Array<{
      company: string;
      position: string;
      startDate: string;
      endDate?: string;
      summary?: string;
      highlights?: string[];
      location?: string;
    }>
  ): Resume => ({
    $schema: null,
    meta: {
      canonical: null,
      version: null,
      last_modified: null,
      job_description: null,
      created_at: '2024-01-01' as IsoDate,
    },
    header: {
      items: [
        { name: 'name', value: 'John Doe' },
        { name: 'email', value: '<EMAIL>' },
      ],
    },
    sections: [
      {
        name: 'work',
        items: workExperiences.map((exp) => ({
          name: exp.company,
          position: exp.position,
          start_date: exp.startDate as IsoDate,
          end_date: exp.endDate ? (exp.endDate as IsoDate) : null,
          summary: exp.summary || null,
          highlights: exp.highlights || null,
          location: exp.location || null,
          description: null,
          url: null,
          positions: null,
        })),
      },
    ],
  });

  it('should extract work experiences from single resume', async () => {
    const resume = createMockResume([
      {
        company: 'Acme Corp',
        position: 'Software Engineer',
        startDate: '2020-01-01',
        endDate: '2023-12-31',
        summary: 'Developed web applications using React',
        highlights: ['Built React components', 'Improved performance by 30%'],
      },
    ]);

    const job = createMockJob();

    const result = await analyzeSingleResumeExperience(
      resume,
      job,
      createMockModel(),
      {
        includeImprovements: false,
      }
    );

    expect(result.experiences).toHaveLength(1);
    expect(result.experiences[0].name).toBe('Acme Corp');
    expect(result.experiences[0].position).toBe('Software Engineer');
    expect(result.experiences[0].summary).toBe(
      'Developed web applications using React'
    );
    expect(result.experiences[0].highlights).toEqual([
      'Built React components',
      'Improved performance by 30%',
    ]);

    expect(result.scoredExperiences).toHaveLength(1);
    expect(result.scoredExperiences[0].relevanceScore).toBe(3); // Default score when ID doesn't match

    expect(result.summary.totalExperiences).toBe(1);
    expect(result.summary.averageRelevanceScore).toBe(3);
    expect(result.summary.highRelevanceCount).toBe(0); // No high relevance (score >= 4)
  });

  it('should handle resume with no work experience', async () => {
    const resume = createMockResume([]);
    const job = createMockJob();

    const result = await analyzeSingleResumeExperience(
      resume,
      job,
      createMockModel()
    );

    expect(result.experiences).toHaveLength(0);
    expect(result.scoredExperiences).toHaveLength(0);
    expect(result.improvements).toHaveLength(0);
    expect(result.summary.totalExperiences).toBe(0);
    expect(result.summary.averageRelevanceScore).toBe(0);
    expect(result.summary.highRelevanceCount).toBe(0);
  });

  it('should sort experiences by start date (most recent first)', async () => {
    const resume = createMockResume([
      {
        company: 'Old Corp',
        position: 'Junior Developer',
        startDate: '2018-01-01',
        endDate: '2020-01-01',
      },
      {
        company: 'New Corp',
        position: 'Senior Developer',
        startDate: '2022-01-01',
        endDate: '2024-01-01',
      },
      {
        company: 'Mid Corp',
        position: 'Developer',
        startDate: '2020-01-01',
        endDate: '2022-01-01',
      },
    ]);

    const job = createMockJob();

    const result = await analyzeSingleResumeExperience(
      resume,
      job,
      createMockModel(),
      {
        includeImprovements: false,
      }
    );

    expect(result.experiences).toHaveLength(3);

    // Should be sorted by start date, most recent first
    expect(result.experiences[0].name).toBe('New Corp'); // 2022
    expect(result.experiences[1].name).toBe('Mid Corp'); // 2020
    expect(result.experiences[2].name).toBe('Old Corp'); // 2018
  });

  it('should generate unique IDs for experiences', async () => {
    const resume = createMockResume([
      {
        company: 'Acme Corp',
        position: 'Software Engineer',
        startDate: '2020-01-01',
      },
      {
        company: 'Beta Inc',
        position: 'Frontend Developer',
        startDate: '2021-01-01',
      },
    ]);

    const job = createMockJob();

    const result = await analyzeSingleResumeExperience(
      resume,
      job,
      createMockModel(),
      {
        includeImprovements: false,
      }
    );

    expect(result.experiences).toHaveLength(2);
    expect(result.experiences[0].id).toBeDefined();
    expect(result.experiences[1].id).toBeDefined();
    expect(result.experiences[0].id).not.toBe(result.experiences[1].id);
  });
});
