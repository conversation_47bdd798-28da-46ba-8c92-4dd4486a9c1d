import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import {
  EXPERIENCE_IMPROVEMENT_STANDALONE_PROMPT,
  formatJobContext,
} from './prompt-components';
import type {
  SingleResumeExperienceAnalysisOptions,
  SingleResumeWorkExperience,
  SingleResumeWorkExperienceImprovement,
} from './single-resume-types';
import { singleResumeExperienceImprovementSchema } from './single-resume-types';

/**
 * Suggests improvements for work experience content from a single resume.
 *
 * This class uses AI to analyze work experience content and suggest
 * improvements to better align with job requirements, without any consolidation logic.
 */
export class SingleResumeExperienceImprover {
  constructor(private model: BaseChatModel) {}

  /**
   * Suggests improvements for work experience content from a single resume.
   *
   * @param experiences - Array of work experiences from single resume to improve
   * @param job - Job posting to optimize content for
   * @param options - Configuration options
   * @returns Promise resolving to array of improvement suggestions
   */
  async improveExperiences(
    experiences: SingleResumeWorkExperience[],
    job: Job,
    options: SingleResumeExperienceAnalysisOptions = {}
  ): Promise<SingleResumeWorkExperienceImprovement[]> {
    if (!experiences.length || !options.includeImprovements) {
      return [];
    }

    const maxImprovements = options.maxImprovements || 10;
    const timeoutMs = options.timeoutMs || 30000;
    const limitedExperiences = experiences.slice(0, maxImprovements);

    try {
      const allImprovements: SingleResumeWorkExperienceImprovement[] = [];

      // Process experiences one at a time for better quality
      for (const experience of limitedExperiences) {
        const improvements = await this.improveExperience(
          experience,
          job,
          timeoutMs
        );
        allImprovements.push(...improvements);
      }

      return allImprovements;
    } catch (error) {
      if (isAIUnavailableError(error) || isTimeoutError(error)) {
        // Return empty array if AI is unavailable
        return [];
      }
      throw error;
    }
  }

  /**
   * Improves a single work experience
   */
  private async improveExperience(
    experience: SingleResumeWorkExperience,
    job: Job,
    timeoutMs: number
  ): Promise<SingleResumeWorkExperienceImprovement[]> {
    const improvements: SingleResumeWorkExperienceImprovement[] = [];

    // Improve summary if it exists
    if (experience.summary) {
      const summaryImprovement = await this.improveField(
        experience,
        'summary',
        experience.summary,
        job,
        timeoutMs
      );
      if (summaryImprovement) {
        improvements.push(summaryImprovement);
      }
    }

    // Improve description if it exists
    if (experience.description) {
      const descriptionImprovement = await this.improveField(
        experience,
        'description',
        experience.description,
        job,
        timeoutMs
      );
      if (descriptionImprovement) {
        improvements.push(descriptionImprovement);
      }
    }

    // Improve highlights if they exist
    if (experience.highlights && experience.highlights.length > 0) {
      const highlightsImprovement = await this.improveField(
        experience,
        'highlights',
        experience.highlights.join('\n'),
        job,
        timeoutMs
      );
      if (highlightsImprovement) {
        improvements.push(highlightsImprovement);
      }
    }

    return improvements;
  }

  /**
   * Improves a specific field of a work experience
   */
  private async improveField(
    experience: SingleResumeWorkExperience,
    field: 'summary' | 'highlights' | 'description',
    content: string,
    job: Job,
    timeoutMs: number
  ): Promise<SingleResumeWorkExperienceImprovement | null> {
    const prompt = ChatPromptTemplate.fromTemplate(
      EXPERIENCE_IMPROVEMENT_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(
      singleResumeExperienceImprovementSchema
    );
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        company: experience.name,
        position: experience.position,
        duration: `${experience.start_date} to ${
          experience.end_date || 'Present'
        }`,
        fieldName: field,
        currentContent: content,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume experience improvement'
    );

    if (!result.hasImprovement || !result.improvedContent) {
      return null;
    }

    return {
      experienceId: experience.id,
      field,
      originalContent: content,
      improvedContent: result.improvedContent,
      reasoning: result.reasoning || 'No specific reasoning provided',
    };
  }
}
