import { z } from 'zod';
import { ResumeEntity } from '../../entities/resume';

/**
 * Represents a work experience from a single resume (no consolidation)
 */
export interface SingleResumeWorkExperience extends ResumeEntity<'work'> {
  // artificially generated
  id: string;
}

/**
 * Represents a work experience with its relevance score from single resume analysis
 */
export interface SingleResumeWorkExperienceScore {
  /** The work experience being scored */
  experience: SingleResumeWorkExperience;
  /** Relevance score from 1-5 (5 being most relevant) */
  relevanceScore: number;
  /** AI reasoning for the assigned score */
  reasoning: string;
}

/**
 * Represents a suggested improvement for work experience content from single resume
 */
export interface SingleResumeWorkExperienceImprovement {
  /** The experience being improved */
  experienceId: string;
  /** The field being improved */
  field: 'summary' | 'highlights' | 'description';
  /** Original content */
  originalContent: string;
  /** Improved content suggestion */
  improvedContent: string;
  /** Reasoning for the improvement */
  reasoning: string;
}

/**
 * Configuration options for single resume experience analysis
 */
export interface SingleResumeExperienceAnalysisOptions {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of experiences per call) */
  batchSize?: number;
  /** Whether to include content improvement suggestions */
  includeImprovements?: boolean;
  /** Maximum number of improvements to suggest */
  maxImprovements?: number;
}

/**
 * Complete analysis result for single resume work experience matching
 */
export interface SingleResumeExperienceAnalysis {
  /** All work experiences from the single resume */
  experiences: SingleResumeWorkExperience[];
  /** Work experiences with relevance scores */
  scoredExperiences: SingleResumeWorkExperienceScore[];
  /** Suggested content improvements */
  improvements: SingleResumeWorkExperienceImprovement[];
  /** Summary statistics */
  summary: {
    /** Total number of work experiences */
    totalExperiences: number;
    /** Average relevance score across all experiences */
    averageRelevanceScore: number;
    /** Number of highly relevant experiences (score >= 4) */
    highRelevanceCount: number;
    /** Number of improvement suggestions provided */
    improvementsProposed: number;
  };
}

/**
 * Schema for AI-powered single resume experience scoring results
 */
export const singleResumeExperienceScoreSchema = z.object({
  scores: z.array(
    z.object({
      experienceId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

/**
 * Schema for AI-powered single resume experience improvement suggestions
 */
export const singleResumeExperienceImprovementSchema = z.object({
  hasImprovement: z.boolean(),
  improvedContent: z.string().optional(),
  reasoning: z.string().max(300).optional(),
});

/**
 * Type definitions for Zod schema inference
 */
export type SingleResumeExperienceScoreResult = z.infer<typeof singleResumeExperienceScoreSchema>;
export type SingleResumeExperienceImprovementResult = z.infer<typeof singleResumeExperienceImprovementSchema>;
