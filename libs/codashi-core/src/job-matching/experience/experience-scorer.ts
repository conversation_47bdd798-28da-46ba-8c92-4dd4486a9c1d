import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import type {
  ConsolidatedWorkExperience,
  ExperienceAnalysisOptions,
  ExperienceScoreResult,
  WorkExperienceScore,
} from './types';
import { experienceScoreSchema } from './types';
import { estimateExperienceTokens } from './utils';

/**
 * Scores work experiences based on their relevance to a target job.
 *
 * This class uses AI to analyze work experiences and assign relevance scores
 * from 1-5 based on how well they align with the job requirements.
 */
export class ExperienceScorer {
  private readonly MAX_SAFE_TOKENS = 8000; // Conservative limit for most models
  private readonly DEFAULT_BATCH_SIZE = 8; // Default number of experiences per LLM call

  constructor(private model: BaseChatModel) {}

  /**
   * Scores work experiences based on relevance to the target job
   *
   * @param experiences - Array of consolidated work experiences to score
   * @param job - Job posting to score against
   * @param options - Configuration options
   * @returns Promise resolving to array of scored experiences
   */
  async scoreExperiences(
    experiences: ConsolidatedWorkExperience[],
    job: Job,
    options: ExperienceAnalysisOptions = {}
  ): Promise<WorkExperienceScore[]> {
    if (!experiences.length) {
      return [];
    }

    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    try {
      // Determine if we should use batching
      const totalTokens = this.estimateTotalTokens(experiences, job);

      if (
        totalTokens <= this.MAX_SAFE_TOKENS &&
        experiences.length <= batchSize
      ) {
        // Process all experiences in a single call
        return await this.scoreBatch(experiences, job, timeoutMs);
      } else {
        // Process in batches
        return await this.scoreInBatches(
          experiences,
          job,
          batchSize,
          timeoutMs
        );
      }
    } catch (error) {
      if (isAIUnavailableError(error) || isTimeoutError(error)) {
        // Return default scores if AI is unavailable
        return this.createDefaultScores(experiences);
      }
      throw error;
    }
  }

  /**
   * Scores a batch of experiences in a single LLM call
   */
  private async scoreBatch(
    experiences: ConsolidatedWorkExperience[],
    job: Job,
    timeoutMs: number
  ): Promise<WorkExperienceScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are an expert career advisor analyzing work experience relevance for job applications.

      Job Title: {jobTitle}
      Job Description: {jobDescription}
      Required Skills: {jobSkills}
      Required Qualifications: {jobQualifications}

      Please score each work experience below on a scale of 1-5 based on relevance to this job:
      - 5: Highly relevant - directly applicable skills, similar role/industry, strong alignment
      - 4: Very relevant - mostly applicable skills, related role/industry, good alignment  
      - 3: Moderately relevant - some applicable skills, somewhat related role/industry
      - 2: Slightly relevant - few applicable skills, loosely related role/industry
      - 1: Not relevant - minimal applicable skills, unrelated role/industry

      Work Experiences to Score:
      {experienceList}

      Consider these factors in your scoring:
      - Role/position similarity to the target job
      - Industry relevance and transferable skills
      - Seniority level alignment
      - Specific accomplishments that demonstrate relevant capabilities
      - Technologies, tools, and methodologies mentioned

      {format_instructions}
    `);

    const parser = StructuredOutputParser.fromZodSchema(experienceScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const experienceList = experiences
      .map((exp, index) => this.formatExperienceForPrompt(exp, index))
      .join('\n\n');

    const result = await withTimeout(
      chain.invoke({
        jobTitle: job.title || 'Not specified',
        jobDescription: job.description || 'Not specified',
        jobSkills: this.formatJobSkills(job),
        jobQualifications: this.formatJobQualifications(job),
        experienceList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Experience scoring'
    );

    return this.mapResultsToScores(result, experiences);
  }

  /**
   * Scores experiences in multiple batches
   */
  private async scoreInBatches(
    experiences: ConsolidatedWorkExperience[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<WorkExperienceScore[]> {
    const allScores: WorkExperienceScore[] = [];

    for (let i = 0; i < experiences.length; i += batchSize) {
      const batch = experiences.slice(i, i + batchSize);
      const batchScores = await this.scoreBatch(batch, job, timeoutMs);
      allScores.push(...batchScores);
    }

    return allScores;
  }

  /**
   * Creates default scores when AI is unavailable
   */
  private createDefaultScores(
    experiences: ConsolidatedWorkExperience[]
  ): WorkExperienceScore[] {
    return experiences.map((experience) => ({
      experience,
      relevanceScore: 3, // Default neutral score
      reasoning: 'AI scoring unavailable - default score assigned',
    }));
  }

  /**
   * Estimates total token count for all experiences and job description
   */
  private estimateTotalTokens(
    experiences: ConsolidatedWorkExperience[],
    job: Job
  ): number {
    let totalTokens = 0;

    // Job description tokens
    totalTokens += (job.title?.length || 0) / 4;
    totalTokens += (job.description?.length || 0) / 4;

    // Experience tokens
    for (const experience of experiences) {
      totalTokens += estimateExperienceTokens(experience);
    }

    // Add buffer for prompt template and response
    totalTokens += 1000;

    return Math.ceil(totalTokens);
  }

  /**
   * Formats an experience for the prompt
   */
  private formatExperienceForPrompt(
    experience: ConsolidatedWorkExperience,
    index: number
  ): string {
    let formatted = `Experience ${index + 1} (ID: ${experience.id}):
Company: ${experience.name}
Position: ${experience.position}
Duration: ${experience.start_date} to ${experience.end_date || 'Present'}`;

    if (experience.location) {
      formatted += `\nLocation: ${experience.location}`;
    }

    if (experience.summary) {
      formatted += `\nSummary: ${experience.summary}`;
    }

    if (experience.highlights && experience.highlights.length > 0) {
      formatted += `\nKey Achievements:\n${experience.highlights
        .map((h) => `• ${h}`)
        .join('\n')}`;
    }

    if (experience.description) {
      formatted += `\nDescription: ${experience.description}`;
    }

    return formatted;
  }

  /**
   * Formats job skills for the prompt
   */
  private formatJobSkills(job: Job): string {
    if (!job.skills || job.skills.length === 0) {
      return 'Not specified';
    }

    return job.skills
      .map((skill) => {
        let formatted = skill.name;
        if (skill.level) {
          formatted += ` (${skill.level})`;
        }
        return formatted;
      })
      .join(', ');
  }

  /**
   * Formats job qualifications for the prompt
   */
  private formatJobQualifications(job: Job): string {
    if (!job.qualifications || job.qualifications.length === 0) {
      return 'Not specified';
    }

    return job.qualifications.join(', ');
  }

  /**
   * Maps AI results back to WorkExperienceScore objects
   */
  private mapResultsToScores(
    result: ExperienceScoreResult,
    experiences: ConsolidatedWorkExperience[]
  ): WorkExperienceScore[] {
    const scores: WorkExperienceScore[] = [];

    for (const scoreResult of result.scores) {
      const experience = experiences.find(
        (exp) => exp.id === scoreResult.experienceId
      );
      if (experience) {
        scores.push({
          experience,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }

    // Add default scores for any missing experiences
    for (const experience of experiences) {
      if (!scores.find((score) => score.experience.id === experience.id)) {
        scores.push({
          experience,
          relevanceScore: 3,
          reasoning: 'Score not provided by AI - default assigned',
        });
      }
    }

    return scores;
  }
}
