import { FakeListChatModel } from '@langchain/core/utils/testing';
import { describe, expect, it } from 'vitest';

import { IsoDate } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { analyzeExperienceMatch } from './analyzer';
import { ExperienceMatchError } from './types';

describe('analyzeExperienceMatch', () => {
  // Create a mock model that provides enough responses for all tests
  const createMockModel = () =>
    new FakeListChatModel({
      responses: [
        // Experience scoring responses (used multiple times)
        JSON.stringify({
          scores: [
            {
              experienceId: 'acme-corp-senior-software-engineer-2020-01-01',
              relevanceScore: 5,
              reasoning:
                'Highly relevant senior software engineering role with modern tech stack',
            },
            {
              experienceId: 'tech-startup-frontend-developer-2018-06-01',
              relevanceScore: 4,
              reasoning:
                'Very relevant frontend development experience with React',
            },
          ],
        }),
        // More scoring responses for other tests
        JSON.stringify({
          scores: [
            {
              experienceId: 'acme-corp-senior-software-engineer-2020-01-01',
              relevanceScore: 5,
              reasoning:
                'Highly relevant senior software engineering role with modern tech stack',
            },
          ],
        }),
        JSON.stringify({
          scores: [
            {
              experienceId: 'acme-corp-senior-software-engineer-2020-01-01',
              relevanceScore: 5,
              reasoning:
                'Highly relevant senior software engineering role with modern tech stack',
            },
            {
              experienceId: 'tech-startup-frontend-developer-2018-06-01',
              relevanceScore: 4,
              reasoning:
                'Very relevant frontend development experience with React',
            },
          ],
        }),
        // Experience improvement responses
        JSON.stringify({
          hasImprovement: true,
          improvedContent:
            'Led development of scalable web applications using React, TypeScript, and Node.js, resulting in 40% performance improvement and enhanced user experience for 10,000+ daily active users.',
          reasoning:
            'Added quantifiable metrics and emphasized leadership and impact',
        }),
        JSON.stringify({
          hasImprovement: false,
          reasoning: 'Content is already well-optimized',
        }),
        // Additional responses for other improvement calls
        JSON.stringify({
          hasImprovement: true,
          improvedContent:
            'Enhanced frontend development experience with React and modern JavaScript frameworks.',
          reasoning: 'Improved clarity and relevance',
        }),
      ],
    });

  const sampleJob: Job = {
    title: 'Senior Frontend Developer',
    company: 'Tech Corp',
    type: 'Full-time',
    date: null,
    description:
      'We are looking for a senior frontend developer with React experience',
    location: null,
    remote: null,
    salary: null,
    experience: null,
    responsibilities: null,
    skills: [
      { name: 'React', level: 'Expert', keywords: ['ReactJS', 'JSX'] },
      { name: 'TypeScript', level: 'Intermediate', keywords: null },
      { name: 'Node.js', level: 'Intermediate', keywords: null },
    ],
    qualifications: ['5+ years frontend development', 'React expertise'],
    meta: null,
    company_meta: {
      type: null,
      size: null,
      tone: null,
      internal_company_id: null,
    },
    notes: null,
    benefits: null,
  };

  const createTestResume = (
    workExperiences: Array<{
      company: string;
      position: string;
      startDate: string;
      endDate?: string;
      summary?: string;
      highlights?: string[];
    }>
  ): Resume => ({
    $schema: null,
    meta: {
      canonical: null,
      version: null,
      last_modified: null,
      job_description: null,
      created_at: '2024-01-01' as IsoDate,
    },
    header: {
      items: [
        { name: 'name', value: 'John Doe' },
        { name: 'title', value: 'Software Engineer' },
        { name: 'email', value: '<EMAIL>' },
      ],
    },
    sections: [
      {
        name: 'work',
        items: workExperiences.map((exp) => ({
          name: exp.company,
          position: exp.position,
          location: null,
          description: null,
          url: null,
          start_date: exp.startDate as IsoDate,
          end_date: exp.endDate ? (exp.endDate as IsoDate) : null,
          summary: exp.summary || null,
          highlights: exp.highlights || null,
          positions: null,
        })),
      },
    ],
  });

  const sampleResume1 = createTestResume([
    {
      company: 'Acme Corp',
      position: 'Senior Software Engineer',
      startDate: '2020-01-01',
      endDate: '2023-12-31',
      summary: 'Developed web applications using React and TypeScript',
      highlights: [
        'Built scalable frontend architecture',
        'Mentored junior developers',
      ],
    },
    {
      company: 'Tech Startup',
      position: 'Frontend Developer',
      startDate: '2018-06-01',
      endDate: '2019-12-31',
      summary: 'Created responsive web interfaces',
      highlights: ['Implemented React components', 'Improved page load times'],
    },
  ]);

  const sampleResume2 = createTestResume([
    {
      company: 'Acme Corp',
      position: 'Senior Software Engineer',
      startDate: '2020-01-01',
      endDate: '2023-12-31',
      summary:
        'Led development of modern web applications with focus on performance',
      highlights: [
        'Architected scalable solutions',
        'Reduced load times by 40%',
      ],
    },
  ]);

  it('should successfully analyze experience matches', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1, sampleResume2],
      sampleJob,
      createMockModel()
    );

    expect(result).toBeDefined();
    expect(result.consolidatedExperiences).toBeDefined();
    expect(result.scoredExperiences).toBeDefined();
    expect(result.improvements).toBeDefined();
    expect(result.summary).toBeDefined();

    // Should consolidate duplicate experiences
    expect(result.consolidatedExperiences).toHaveLength(2); // Acme Corp + Tech Startup

    // Should have scores for all experiences
    expect(result.scoredExperiences).toHaveLength(2);
    expect(result.scoredExperiences[0].relevanceScore).toBeGreaterThan(0);
    expect(result.scoredExperiences[0].reasoning).toBeDefined();

    // Should have summary statistics
    expect(result.summary.totalExperiences).toBe(2);
    expect(result.summary.averageRelevanceScore).toBeGreaterThan(0);
    expect(result.summary.highRelevanceCount).toBeGreaterThanOrEqual(0);
  });

  it('should consolidate duplicate experiences correctly', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1, sampleResume2],
      sampleJob,
      createMockModel()
    );

    // Find the consolidated Acme Corp experience
    const acmeExperience = result.consolidatedExperiences.find(
      (exp) => exp.name === 'Acme Corp'
    );

    expect(acmeExperience).toBeDefined();
    expect(acmeExperience?.sourceResumes).toEqual([0, 1]); // From both resumes
    expect(acmeExperience?.highlights).toContain(
      'Built scalable frontend architecture'
    );
    expect(acmeExperience?.highlights).toContain('Reduced load times by 40%');

    // Should use the longer/more detailed summary
    expect(acmeExperience?.summary).toBe(
      'Led development of modern web applications with focus on performance'
    );
  });

  it('should sort experiences by start date (most recent first)', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1],
      sampleJob,
      createMockModel()
    );

    expect(result.consolidatedExperiences).toHaveLength(2);

    // Should be sorted by start date, most recent first
    const dates = result.consolidatedExperiences.map(
      (exp) => new Date(exp.start_date)
    );
    expect(dates[0].getTime()).toBeGreaterThan(dates[1].getTime());
  });

  it('should handle resumes with no work experience', async () => {
    const emptyResume = createTestResume([]);

    const result = await analyzeExperienceMatch(
      [emptyResume],
      sampleJob,
      createMockModel()
    );

    expect(result.consolidatedExperiences).toHaveLength(0);
    expect(result.scoredExperiences).toHaveLength(0);
    expect(result.summary.totalExperiences).toBe(0);
    expect(result.summary.averageRelevanceScore).toBe(0);
  });

  it('should include improvements when requested', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1],
      sampleJob,
      createMockModel(),
      { includeImprovements: true, maxImprovements: 2 }
    );

    expect(result.improvements).toBeDefined();
    // Note: Actual improvements depend on AI response, but structure should be correct
    if (result.improvements.length > 0) {
      const improvement = result.improvements[0];
      expect(improvement.experienceId).toBeDefined();
      expect(improvement.field).toMatch(/^(summary|highlights|description)$/);
      expect(improvement.originalContent).toBeDefined();
      expect(improvement.improvedContent).toBeDefined();
      expect(improvement.reasoning).toBeDefined();
      expect(improvement.diffPreview).toBeDefined();
    }
  });

  it('should not include improvements when not requested', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1],
      sampleJob,
      createMockModel(),
      { includeImprovements: false }
    );

    expect(result.improvements).toHaveLength(0);
  });

  it('should handle timeout errors gracefully', async () => {
    const timeoutModel = new FakeListChatModel({
      responses: [], // Empty responses will cause timeout
    });

    await expect(
      analyzeExperienceMatch([sampleResume1], sampleJob, timeoutModel, {
        timeoutMs: 100, // Very short timeout
      })
    ).rejects.toThrow(ExperienceMatchError);
  });

  it('should calculate summary statistics correctly', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1],
      sampleJob,
      createMockModel()
    );

    expect(result.summary.totalExperiences).toBe(
      result.consolidatedExperiences.length
    );
    expect(result.summary.averageRelevanceScore).toBeGreaterThan(0);
    expect(result.summary.averageRelevanceScore).toBeLessThanOrEqual(5);

    const highRelevanceActual = result.scoredExperiences.filter(
      (exp) => exp.relevanceScore >= 4
    ).length;
    expect(result.summary.highRelevanceCount).toBe(highRelevanceActual);
  });

  it('should handle custom batch size', async () => {
    const result = await analyzeExperienceMatch(
      [sampleResume1],
      sampleJob,
      createMockModel(),
      { batchSize: 1 } // Force smaller batches
    );

    expect(result.scoredExperiences).toHaveLength(2);
    expect(
      result.scoredExperiences.every((exp) => exp.relevanceScore > 0)
    ).toBe(true);
  });
});
