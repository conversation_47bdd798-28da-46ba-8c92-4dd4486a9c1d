import type { IsoDate } from '@awe/core';
import type { SingleResumeExperienceAnalysis } from './single-resume-types';
import type { ExperienceAnalysis } from './types';

/**
 * Reduces multiple single-resume experience analyses into a consolidated result.
 *
 * This function takes the results from analyzing multiple resumes individually
 * and combines them into the format expected by the existing multi-resume API,
 * maintaining backward compatibility while using the new single-resume architecture.
 *
 * @param analyses - Array of single-resume experience analysis results
 * @returns Consolidated experience match analysis in the original format
 *
 * @example
 * ```typescript
 * const singleResumeResults = await Promise.all(
 *   resumes.map(resume => analyzeSingleResumeExperience(resume, job, model))
 * );
 *
 * const consolidatedResult = reduceExperienceAnalyses(singleResumeResults);
 * console.log(`Total experiences: ${consolidatedResult.consolidatedExperiences.length}`);
 * ```
 */
export function reduceExperienceAnalyses(
  analyses: SingleResumeExperienceAnalysis[]
): ExperienceAnalysis {
  if (analyses.length === 0) {
    return createEmptyExperienceAnalysis();
  }

  // Collect all experiences from all resumes
  const allExperiences = analyses.flatMap((analysis) => analysis.experiences);

  // Consolidate experiences (remove duplicates, merge similar ones)
  const consolidatedExperiences = consolidateExperiences(allExperiences);

  // Convert single-resume scored experiences to consolidated format
  const consolidatedScoredExperiences = analyses.flatMap(
    (analysis, resumeIndex) =>
      analysis.scoredExperiences.map((score) => ({
        experience: {
          ...score.experience,
          sourceResumes: [resumeIndex], // Track which resume this came from
        },
        relevanceScore: score.relevanceScore,
        reasoning: score.reasoning,
      }))
  );

  // Calculate summary statistics
  const summary = calculateConsolidatedSummary(consolidatedScoredExperiences);

  // Convert single-resume improvements to consolidated format
  const consolidatedImprovements = analyses.flatMap((analysis) =>
    (analysis.improvements || []).map((improvement) => ({
      ...improvement,
      diffPreview: '', // Add required diffPreview field (empty for now)
    }))
  );

  return {
    consolidatedExperiences,
    scoredExperiences: consolidatedScoredExperiences,
    improvements: consolidatedImprovements,
    summary,
  };
}

/**
 * Creates an empty experience analysis result
 */
function createEmptyExperienceAnalysis(): ExperienceAnalysis {
  return {
    consolidatedExperiences: [],
    scoredExperiences: [],
    improvements: [],
    summary: {
      totalExperiences: 0,
      averageRelevanceScore: 0,
      highRelevanceCount: 0,
      improvementsProposed: 0,
    },
  };
}

/**
 * Consolidates experiences from multiple resumes, removing duplicates and merging similar ones
 */
function consolidateExperiences(
  experiences: SingleResumeExperienceAnalysis['experiences']
): ExperienceAnalysis['consolidatedExperiences'] {
  if (experiences.length === 0) {
    return [];
  }

  const consolidated: ExperienceAnalysis['consolidatedExperiences'] = [];
  const processedIds = new Set<string>();

  for (const experience of experiences) {
    if (processedIds.has(experience.id)) {
      continue;
    }

    // Find similar experiences (same company and overlapping dates)
    const similarExperiences = experiences.filter(
      (exp) =>
        exp.id !== experience.id &&
        !processedIds.has(exp.id) &&
        areSimilarExperiences(experience, exp)
    );

    if (similarExperiences.length > 0) {
      // Merge similar experiences
      const merged = mergeExperiences(experience, similarExperiences);
      consolidated.push(merged);

      // Mark all merged experiences as processed
      processedIds.add(experience.id);
      similarExperiences.forEach((exp) => processedIds.add(exp.id));
    } else {
      // Add experience as-is
      consolidated.push({
        id: experience.id,
        name: experience.name,
        position: experience.position,
        start_date: experience.start_date,
        end_date: experience.end_date,
        summary: experience.summary,
        description: experience.description,
        highlights: experience.highlights,
        location: experience.location,
        url: experience.url,
        positions: null,
        sourceResumes: [0], // Default to first resume
      });
      processedIds.add(experience.id);
    }
  }

  // Sort by start date (most recent first)
  return consolidated.sort((a, b) => {
    const dateA = new Date(a.start_date);
    const dateB = new Date(b.start_date);
    return dateB.getTime() - dateA.getTime();
  });
}

/**
 * Checks if two experiences are similar enough to be merged
 */
function areSimilarExperiences(
  exp1: SingleResumeExperienceAnalysis['experiences'][0],
  exp2: SingleResumeExperienceAnalysis['experiences'][0]
): boolean {
  // Same company
  const company1 = exp1.name?.toLowerCase() || '';
  const company2 = exp2.name?.toLowerCase() || '';
  if (company1 !== company2) {
    return false;
  }

  // Similar position (allow for minor variations)
  const pos1 = (exp1.position?.toLowerCase() || '').replace(/[^\w\s]/g, '');
  const pos2 = (exp2.position?.toLowerCase() || '').replace(/[^\w\s]/g, '');
  const positionSimilarity = calculateStringSimilarity(pos1, pos2);

  if (positionSimilarity < 0.7) {
    return false;
  }

  // Overlapping or adjacent date ranges
  const start1 = new Date(exp1.start_date);
  const end1 = exp1.end_date ? new Date(exp1.end_date) : new Date();
  const start2 = new Date(exp2.start_date);
  const end2 = exp2.end_date ? new Date(exp2.end_date) : new Date();

  // Check for overlap or adjacency (within 3 months)
  const overlap = Math.max(
    0,
    Math.min(end1.getTime(), end2.getTime()) -
      Math.max(start1.getTime(), start2.getTime())
  );
  const adjacency = Math.min(
    Math.abs(start1.getTime() - end2.getTime()),
    Math.abs(start2.getTime() - end1.getTime())
  );

  return overlap > 0 || adjacency <= 90 * 24 * 60 * 60 * 1000; // 3 months in milliseconds
}

/**
 * Merges similar experiences into a single consolidated experience
 */
function mergeExperiences(
  primary: SingleResumeExperienceAnalysis['experiences'][0],
  similar: SingleResumeExperienceAnalysis['experiences']
): ExperienceAnalysis['consolidatedExperiences'][0] {
  const allExperiences = [primary, ...similar];

  // Use the earliest start date and latest end date
  const startDates = allExperiences.map((exp) => new Date(exp.start_date));
  const endDates = allExperiences
    .map((exp) => (exp.end_date ? new Date(exp.end_date) : null))
    .filter((date) => date !== null) as Date[];

  const earliestStart = new Date(
    Math.min(...startDates.map((d) => d.getTime()))
  );
  const latestEnd =
    endDates.length > 0
      ? new Date(Math.max(...endDates.map((d) => d.getTime())))
      : null;

  // Merge highlights (remove duplicates)
  const allHighlights = allExperiences.flatMap((exp) => exp.highlights || []);
  const uniqueHighlights = Array.from(new Set(allHighlights));

  // Use the longest description and summary
  const longestDescription = allExperiences.sort(
    (a, b) => (b.description || '').length - (a.description || '').length
  )[0].description;
  const longestSummary = allExperiences.sort(
    (a, b) => (b.summary || '').length - (a.summary || '').length
  )[0].summary;

  return {
    id: primary.id, // Keep primary ID
    name: primary.name,
    position: primary.position,
    start_date: earliestStart.toISOString().split('T')[0] as IsoDate,
    end_date: latestEnd
      ? (latestEnd.toISOString().split('T')[0] as IsoDate)
      : null,
    summary: longestSummary,
    description: longestDescription,
    highlights: uniqueHighlights,
    location: primary.location,
    url: primary.url,
    positions: null,
    sourceResumes: [0], // Default to first resume
  };
}

/**
 * Calculates string similarity using a simple algorithm
 */
function calculateStringSimilarity(str1: string, str2: string): number {
  const words1 = str1.split(/\s+/);
  const words2 = str2.split(/\s+/);

  const commonWords = words1.filter((word) =>
    words2.some((w2) => w2.includes(word) || word.includes(w2))
  );

  return commonWords.length / Math.max(words1.length, words2.length);
}

/**
 * Calculates consolidated summary statistics
 */
function calculateConsolidatedSummary(
  scoredExperiences: SingleResumeExperienceAnalysis['scoredExperiences']
): ExperienceAnalysis['summary'] {
  if (scoredExperiences.length === 0) {
    return {
      totalExperiences: 0,
      averageRelevanceScore: 0,
      highRelevanceCount: 0,
      improvementsProposed: 0,
    };
  }

  const totalExperiences = scoredExperiences.length;
  const totalScore = scoredExperiences.reduce(
    (sum, exp) => sum + exp.relevanceScore,
    0
  );
  const averageRelevanceScore = totalScore / totalExperiences;

  const highRelevanceCount = scoredExperiences.filter(
    (exp) => exp.relevanceScore >= 4
  ).length;

  return {
    totalExperiences,
    averageRelevanceScore: Math.round(averageRelevanceScore * 100) / 100, // Round to 2 decimal places
    highRelevanceCount,
    improvementsProposed: 0, // TODO: Count improvements from analyses
  };
}

/**
 * Helper function to get experiences by relevance level
 */
export function getExperiencesByRelevance(
  analysis: ExperienceAnalysis,
  level: 'high' | 'medium' | 'low'
) {
  const threshold = level === 'high' ? 4 : level === 'medium' ? 2 : 0;
  const maxThreshold = level === 'high' ? 5 : level === 'medium' ? 4 : 2;

  return analysis.scoredExperiences.filter(
    (exp) =>
      exp.relevanceScore >= threshold && exp.relevanceScore < maxThreshold
  );
}

/**
 * Helper function to get the most relevant experiences
 */
export function getTopExperiences(analysis: ExperienceAnalysis, count = 5) {
  return analysis.scoredExperiences
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, count);
}
