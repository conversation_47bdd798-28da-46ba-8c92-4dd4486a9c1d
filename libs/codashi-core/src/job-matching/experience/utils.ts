import type { ConsolidatedWorkExperience } from './types';

/**
 * Normalizes company names for comparison
 */
export function normalizeCompanyName(name: string | undefined): string {
  if (!name) return '';
  return name
    .toLowerCase()
    .trim()
    .replace(/\b(inc|llc|ltd|corp|corporation|company|co)\b\.?/g, '')
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Normalizes position titles for comparison
 */
export function normalizePositionTitle(title: string | undefined): string {
  if (!title) return '';
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Checks if two date ranges overlap significantly
 */
export function hasDateOverlap(
  start1: string,
  end1: string | undefined,
  start2: string,
  end2: string | undefined
): boolean {
  const date1Start = new Date(start1);
  const date1End = end1 ? new Date(end1) : new Date();
  const date2Start = new Date(start2);
  const date2End = end2 ? new Date(end2) : new Date();

  // Check if there's any overlap
  const overlapStart = new Date(
    Math.max(date1Start.getTime(), date2Start.getTime())
  );
  const overlapEnd = new Date(Math.min(date1End.getTime(), date2End.getTime()));

  if (overlapStart > overlapEnd) {
    return false; // No overlap
  }

  // Calculate overlap duration in months
  const overlapMonths =
    (overlapEnd.getFullYear() - overlapStart.getFullYear()) * 12 +
    (overlapEnd.getMonth() - overlapStart.getMonth());

  // Consider significant if overlap is at least 3 months
  return overlapMonths >= 3;
}

/**
 * Generates a unique ID for a work experience
 */
export function generateExperienceId(
  company: string | undefined,
  position: string | undefined,
  startDate: string | undefined
): string {
  const normalized = `${normalizeCompanyName(company)}-${normalizePositionTitle(
    position
  )}-${startDate || 'unknown'}`;
  return normalized.replace(/[^\w-]/g, '').substring(0, 50);
}

/**
 * Estimates token count for a work experience (rough approximation)
 */
export function estimateExperienceTokens(
  experience: ConsolidatedWorkExperience
): number {
  let tokenCount = 0;

  tokenCount += (experience.name?.length || 0) / 4;
  tokenCount += (experience.position?.length || 0) / 4;
  tokenCount += (experience.summary?.length || 0) / 4;
  tokenCount += (experience.description?.length || 0) / 4;

  if (experience.highlights) {
    tokenCount += experience.highlights.join(' ').length / 4;
  }

  return Math.ceil(tokenCount);
}

/**
 * Creates a simple diff preview between original and improved content
 */
export function createDiffPreview(original: string, improved: string): string {
  // Simple diff - just show before and after for now
  // Could be enhanced with more sophisticated diff algorithms
  const maxLength = 100;

  const truncatedOriginal =
    original.length > maxLength
      ? original.substring(0, maxLength) + '...'
      : original;

  const truncatedImproved =
    improved.length > maxLength
      ? improved.substring(0, maxLength) + '...'
      : improved;

  return `- ${truncatedOriginal}\n+ ${truncatedImproved}`;
}
