import { describe, it, expect, beforeEach } from 'vitest';
import { FakeListChatModel } from '@langchain/core/utils/testing';
import type { IsoDate } from '@awe/core';

import type { Job } from '../entities/job';
import type { Resume } from '../entities/resume';
import { analyzeExperienceMatch } from './experience/analyzer';
import { analyzeSkillMatch } from './skills/analyzer';
import { findBestTitleMatch } from './title/analyzer';

describe('Job Matching Integration Tests', () => {
  let mockModel: FakeListChatModel;
  let mockJob: Job;
  let mockResumes: Resume[];

  beforeEach(() => {
    mockModel = new FakeListChatModel({
      responses: [
        // Experience scoring responses
        JSON.stringify({
          scores: [
            { experienceId: 'exp1', relevanceScore: 4, reasoning: 'Highly relevant' },
            { experienceId: 'exp2', relevanceScore: 3, reasoning: 'Moderately relevant' },
          ],
        }),
        JSON.stringify({
          scores: [
            { experienceId: 'exp3', relevanceScore: 5, reasoning: 'Perfect match' },
          ],
        }),
        // Skills extraction responses
        JSON.stringify({ skills: ['JavaScript', 'React', 'Node.js'] }),
        JSON.stringify({ skills: ['Python', 'Django', 'PostgreSQL'] }),
        // Transferable skills analysis responses
        JSON.stringify({
          matches: [
            { resumeSkill: 'Vue.js', jobSkill: 'React', confidenceRating: 2, reasoning: 'Similar frameworks' },
          ],
        }),
        JSON.stringify({
          matches: [
            { resumeSkill: 'Flask', jobSkill: 'Express.js', confidenceRating: 2, reasoning: 'Web frameworks' },
          ],
        }),
        // Title analysis responses
        JSON.stringify({
          confidence: 0.85,
          reasoning: 'Strong match',
          isGoodMatch: true,
          suggestedTitle: 'Senior Software Developer',
        }),
        JSON.stringify({
          confidence: 0.75,
          reasoning: 'Good match',
          isGoodMatch: true,
          suggestedTitle: 'Senior Full Stack Developer',
        }),
      ],
    });

    mockJob = {
      title: 'Senior Software Developer',
      description: 'We are looking for a senior software developer with experience in modern web technologies.',
      skills: [
        { name: 'JavaScript', keywords: ['JS', 'ES6'], category: 'Programming' },
        { name: 'React', keywords: ['ReactJS'], category: 'Frontend' },
        { name: 'Node.js', keywords: ['NodeJS'], category: 'Backend' },
        { name: 'Express.js', keywords: ['Express'], category: 'Backend' },
      ],
      qualifications: ["Bachelor's degree in Computer Science", '5+ years of experience'],
      company: 'Tech Corp',
      location: 'San Francisco, CA',
      salary_range: null,
      employment_type: 'full-time',
      remote_policy: 'hybrid',
      posted_date: '2024-01-01' as IsoDate,
      application_deadline: null,
      benefits: null,
      notes: null,
    };

    mockResumes = [
      {
        $schema: null,
        meta: {
          canonical: null,
          version: null,
          last_modified: null,
          job_description: null,
          created_at: '2024-01-01' as IsoDate,
        },
        header: {
          items: [
            { name: 'name', value: 'John Doe' },
            { name: 'title', value: 'Frontend Developer' },
            { name: 'email', value: '<EMAIL>' },
          ],
        },
        sections: [
          {
            name: 'work',
            items: [
              {
                name: 'Tech Startup A',
                position: 'Frontend Developer',
                start_date: '2020-01-01' as IsoDate,
                end_date: '2022-12-31' as IsoDate,
                summary: 'Developed React applications with modern JavaScript',
                description: 'Built responsive web applications using React, JavaScript, and CSS',
                highlights: ['Improved app performance by 40%', 'Led team of 3 developers'],
                location: 'San Francisco, CA',
                url: null,
              },
              {
                name: 'Tech Startup B',
                position: 'Junior Developer',
                start_date: '2018-06-01' as IsoDate,
                end_date: '2019-12-31' as IsoDate,
                summary: 'Started career building web applications',
                description: 'Learned modern web development practices',
                highlights: ['Completed 5 major projects', 'Mentored 2 interns'],
                location: 'San Francisco, CA',
                url: null,
              },
            ],
          },
          {
            name: 'skills',
            items: [
              { name: 'JavaScript', level: 'Expert', keywords: ['ES6', 'TypeScript'] },
              { name: 'React', level: 'Advanced', keywords: ['Hooks', 'Redux'] },
              { name: 'Vue.js', level: 'Intermediate', keywords: ['Vuex'] },
            ],
          },
        ],
      },
      {
        $schema: null,
        meta: {
          canonical: null,
          version: null,
          last_modified: null,
          job_description: null,
          created_at: '2024-01-01' as IsoDate,
        },
        header: {
          items: [
            { name: 'name', value: 'Jane Smith' },
            { name: 'title', value: 'Full Stack Developer' },
            { name: 'email', value: '<EMAIL>' },
          ],
        },
        sections: [
          {
            name: 'work',
            items: [
              {
                name: 'Enterprise Corp',
                position: 'Full Stack Developer',
                start_date: '2021-01-01' as IsoDate,
                end_date: null,
                summary: 'Building scalable web applications with Python and JavaScript',
                description: 'Developed full-stack applications using Python, Django, and React',
                highlights: ['Architected microservices system', 'Reduced load times by 60%'],
                location: 'Remote',
                url: null,
              },
            ],
          },
          {
            name: 'skills',
            items: [
              { name: 'Python', level: 'Expert', keywords: ['Django', 'Flask'] },
              { name: 'JavaScript', level: 'Advanced', keywords: ['Node.js'] },
              { name: 'PostgreSQL', level: 'Intermediate', keywords: ['SQL'] },
            ],
          },
        ],
      },
    ];
  });

  describe('Experience Analysis Integration', () => {
    it('should analyze experience matches using new single-resume architecture', async () => {
      const result = await analyzeExperienceMatch(
        mockResumes,
        mockJob,
        mockModel,
        { includeImprovements: true }
      );

      // Should consolidate experiences from both resumes
      expect(result.experiences.length).toBeGreaterThan(0);
      expect(result.experiences.length).toBeLessThanOrEqual(3); // Max 3 experiences from 2 resumes

      // Should have scored experiences
      expect(result.scoredExperiences.length).toBeGreaterThan(0);
      expect(result.scoredExperiences.every(se => se.relevanceScore >= 1 && se.relevanceScore <= 5)).toBe(true);

      // Should calculate summary correctly
      expect(result.summary.totalExperiences).toBe(result.experiences.length);
      expect(result.summary.averageRelevanceScore).toBeGreaterThan(0);
      expect(result.summary.highRelevanceCount).toBeGreaterThanOrEqual(0);

      // Should maintain backward compatibility with existing API
      expect(result).toHaveProperty('experiences');
      expect(result).toHaveProperty('scoredExperiences');
      expect(result).toHaveProperty('improvements');
      expect(result).toHaveProperty('summary');
    });

    it('should handle empty resume list', async () => {
      const result = await analyzeExperienceMatch([], mockJob, mockModel);

      expect(result.experiences).toHaveLength(0);
      expect(result.scoredExperiences).toHaveLength(0);
      expect(result.improvements).toHaveLength(0);
      expect(result.summary.totalExperiences).toBe(0);
      expect(result.summary.averageRelevanceScore).toBe(0);
    });
  });

  describe('Skills Analysis Integration', () => {
    it('should analyze skill matches using new single-resume architecture', async () => {
      const result = await analyzeSkillMatch(
        mockResumes,
        mockJob,
        mockModel,
        { includeSourceDetails: true }
      );

      // Should extract skills from both resumes
      expect(result.resumeSkills.length).toBeGreaterThan(0);

      // Should find direct matches
      expect(result.directMatches.length).toBeGreaterThan(0);
      expect(result.directMatches.every(dm => dm.confidence >= 0 && dm.confidence <= 1)).toBe(true);

      // Should identify transferable skills
      expect(result.transferableSkills.length).toBeGreaterThanOrEqual(0);

      // Should identify missing skills
      expect(result.missingSkills.length).toBeGreaterThanOrEqual(0);

      // Should calculate summary correctly
      expect(result.summary.totalResumeSkills).toBe(result.resumeSkills.length);
      expect(result.summary.directMatchCount).toBe(result.directMatches.length);
      expect(result.summary.transferableMatchCount).toBe(result.transferableSkills.length);
      expect(result.summary.missingSkillCount).toBe(result.missingSkills.length);

      // Should maintain backward compatibility
      expect(result).toHaveProperty('resumeSkills');
      expect(result).toHaveProperty('directMatches');
      expect(result).toHaveProperty('transferableSkills');
      expect(result).toHaveProperty('missingSkills');
      expect(result).toHaveProperty('summary');
    });

    it('should handle resumes with no skills section', async () => {
      const resumesWithoutSkills = mockResumes.map(resume => ({
        ...resume,
        sections: resume.sections.filter(section => section.name !== 'skills'),
      }));

      const result = await analyzeSkillMatch(resumesWithoutSkills, mockJob, mockModel);

      expect(result.resumeSkills).toHaveLength(0);
      expect(result.directMatches).toHaveLength(0);
      expect(result.summary.totalResumeSkills).toBe(0);
    });
  });

  describe('Title Analysis Integration', () => {
    it('should find best title match using new single-resume architecture', async () => {
      const result = await findBestTitleMatch(
        mockResumes,
        mockJob,
        mockModel,
        { suggestImprovements: true }
      );

      // Should return the best matching title
      expect(result).toHaveProperty('bestMatch');
      expect(result.bestMatch.originalTitle).toBeDefined();
      expect(result.bestMatch.confidence).toBeGreaterThan(0);
      expect(result.bestMatch.confidence).toBeLessThanOrEqual(1);

      // Should include all title analyses
      expect(result.allMatches).toHaveLength(mockResumes.length);
      expect(result.allMatches.every(match => match.confidence >= 0 && match.confidence <= 1)).toBe(true);

      // Should calculate summary correctly
      expect(result.summary.averageConfidence).toBeGreaterThan(0);
      expect(result.summary.bestConfidence).toBeGreaterThanOrEqual(result.summary.averageConfidence);
      expect(result.summary.recommendedTitles.length).toBeGreaterThanOrEqual(0);

      // Should maintain backward compatibility
      expect(result).toHaveProperty('bestMatch');
      expect(result).toHaveProperty('allMatches');
      expect(result).toHaveProperty('summary');
    });

    it('should handle resumes without titles', async () => {
      const resumesWithoutTitles = mockResumes.map(resume => ({
        ...resume,
        header: {
          items: resume.header.items.filter(item => item.name !== 'title'),
        },
      }));

      // Should throw error for resumes without titles
      await expect(
        findBestTitleMatch(resumesWithoutTitles, mockJob, mockModel)
      ).rejects.toThrow();
    });
  });

  describe('End-to-End Integration', () => {
    it('should produce consistent results across all analyzers', async () => {
      const [experienceResult, skillsResult, titleResult] = await Promise.all([
        analyzeExperienceMatch(mockResumes, mockJob, mockModel),
        analyzeSkillMatch(mockResumes, mockJob, mockModel),
        findBestTitleMatch(mockResumes, mockJob, mockModel),
      ]);

      // All analyzers should process the same number of resumes
      expect(titleResult.allMatches).toHaveLength(mockResumes.length);

      // Results should be internally consistent
      expect(experienceResult.experiences.length).toBeGreaterThanOrEqual(0);
      expect(skillsResult.resumeSkills.length).toBeGreaterThanOrEqual(0);
      expect(titleResult.bestMatch.confidence).toBeGreaterThan(0);

      // Summary statistics should be reasonable
      expect(experienceResult.summary.averageRelevanceScore).toBeGreaterThanOrEqual(0);
      expect(skillsResult.summary.overallCoverage).toBeGreaterThanOrEqual(0);
      expect(titleResult.summary.averageConfidence).toBeGreaterThan(0);
    });

    it('should handle edge cases gracefully', async () => {
      // Test with minimal resume data
      const minimalResume: Resume = {
        $schema: null,
        meta: {
          canonical: null,
          version: null,
          last_modified: null,
          job_description: null,
          created_at: '2024-01-01' as IsoDate,
        },
        header: {
          items: [
            { name: 'name', value: 'Test User' },
            { name: 'title', value: 'Developer' },
          ],
        },
        sections: [],
      };

      const [experienceResult, skillsResult, titleResult] = await Promise.all([
        analyzeExperienceMatch([minimalResume], mockJob, mockModel),
        analyzeSkillMatch([minimalResume], mockJob, mockModel),
        findBestTitleMatch([minimalResume], mockJob, mockModel),
      ]);

      // Should handle minimal data without errors
      expect(experienceResult.experiences).toHaveLength(0);
      expect(skillsResult.resumeSkills).toHaveLength(0);
      expect(titleResult.bestMatch.originalTitle).toBe('Developer');
    });
  });
});
