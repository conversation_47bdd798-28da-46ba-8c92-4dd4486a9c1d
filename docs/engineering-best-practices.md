# Engineering Best Practices

## Type Safety & Validation

1. **Prefer Strict Types**  
   Use TypeScript's type system as the first line of defense:

   - Define precise types for all inputs/outputs
   - Use utility types (`Partial`, `Pick`, etc.) for complex scenarios
   - Leverage branded types for domain-specific constraints

2. **Runtime Checks**  
   Only add runtime validation when:
   - Handling external/untrusted input (APIs, user input, etc.)
   - Dealing with type boundaries (e.g., API endpoints)
   - Checking complex invariants that can't be expressed in types

## Type Casting & Any Usage

1. **Avoid Type Assertions**  
   Prefer type inference and proper typing over `as` assertions:

   ```typescript
   // Bad ❌
   const value = JSON.parse(raw) as MyType;

   // Better ✅
   const value = parseMyType(raw); // Uses Zod/io-ts validator
   ```

2. **Ban `any` Type**  
   Never use `any` unless absolutely unavoidable:

   ```typescript
   // Bad ❌
   function dangerous(data: any) { ... }

   // Better ✅
   function safe(data: unknown) {
     if (isValid(data)) { ... } // Proper type guard
   }
   ```

3. **Exception Justification**  
   When forced to use `any` or type assertions:
   - Add a `// @ts-expect-error` comment explaining why
   - Document the rationale in a code comment
   - Include ticket number if addressing tech debt
   ```typescript
   // Temporary workaround for SDK types mismatch (#PROJ-123)
   const response = (await sdk.call()) as unknown as CorrectType;
   ```

## Validation Layers

```typescript
// Good for external data
const validateInput = (input: unknown) => {
  return schema.parse(input); // Using Zod
};

// Unnecessary for internal type-safe code
function processUser(user: User) {
  // No need to check user properties - type system guarantees them
}
```

## Example: TitleMatchAnalyzer

```typescript
// Before: Runtime checks + type annotations
validateInputs(resumeVariations, job);

// After: Type-driven approach
function findBestTitleMatch(
  resumeVariations: Resume[], // Type enforces array structure
  job: Job // Type ensures valid Job object
) {
  // No runtime checks needed - type system enforces constraints
}
```
